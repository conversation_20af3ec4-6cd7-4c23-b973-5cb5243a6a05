<?php
/**
 * CFB Calculator Invoices
 * Handles invoice management and generation
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Invoices {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_cfb_save_invoice', array($this, 'ajax_save_invoice'));
        add_action('wp_ajax_cfb_delete_invoice', array($this, 'ajax_delete_invoice'));
        add_action('wp_ajax_nopriv_cfb_save_invoice', array($this, 'ajax_save_invoice'));
    }
    
    /**
     * Render invoices page
     */
    public function render_invoices_page() {
        $action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'list';
        
        switch ($action) {
            case 'create':
                $this->render_create_invoice_page();
                break;
            case 'edit':
                $this->render_edit_invoice_page();
                break;
            case 'view':
                $this->render_view_invoice_page();
                break;
            default:
                $this->render_invoices_list();
                break;
        }
    }
    
    /**
     * Render invoices list
     */
    private function render_invoices_list() {
        $invoices = $this->get_invoices();
        ?>
        <div class="wrap cfb-invoices">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-media-document"></span>
                <?php _e('Invoices', 'cfb-calculator'); ?>
            </h1>
            <a href="<?php echo admin_url('admin.php?page=cfb-calculator-invoices&action=create'); ?>" class="page-title-action">
                <?php _e('Create New Invoice', 'cfb-calculator'); ?>
            </a>
            
            <?php if (empty($invoices)): ?>
                <div class="cfb-empty-state">
                    <div class="cfb-empty-icon">
                        <span class="dashicons dashicons-media-document"></span>
                    </div>
                    <h2><?php _e('No invoices found', 'cfb-calculator'); ?></h2>
                    <p><?php _e('Create your first invoice to get started.', 'cfb-calculator'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=cfb-calculator-invoices&action=create'); ?>" class="button button-primary">
                        <?php _e('Create Your First Invoice', 'cfb-calculator'); ?>
                    </a>
                </div>
            <?php else: ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Invoice #', 'cfb-calculator'); ?></th>
                            <th><?php _e('Customer', 'cfb-calculator'); ?></th>
                            <th><?php _e('Form', 'cfb-calculator'); ?></th>
                            <th><?php _e('Total', 'cfb-calculator'); ?></th>
                            <th><?php _e('Status', 'cfb-calculator'); ?></th>
                            <th><?php _e('Date', 'cfb-calculator'); ?></th>
                            <th><?php _e('Actions', 'cfb-calculator'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($invoices as $invoice): ?>
                            <tr>
                                <td>
                                    <strong><?php echo esc_html($invoice->invoice_number); ?></strong>
                                </td>
                                <td>
                                    <div class="cfb-customer-info">
                                        <strong><?php echo esc_html($invoice->customer_name); ?></strong><br>
                                        <small><?php echo esc_html($invoice->customer_email); ?></small>
                                    </div>
                                </td>
                                <td><?php echo esc_html($invoice->form_name); ?></td>
                                <td><?php echo $this->format_currency($invoice->total_amount); ?></td>
                                <td>
                                    <span class="cfb-status-badge cfb-status-<?php echo esc_attr($invoice->status); ?>">
                                        <?php echo esc_html(ucfirst($invoice->status)); ?>
                                    </span>
                                </td>
                                <td><?php echo date_i18n(get_option('date_format'), strtotime($invoice->created_at)); ?></td>
                                <td>
                                    <div class="cfb-invoice-actions">
                                        <a href="<?php echo admin_url('admin.php?page=cfb-calculator-invoices&action=view&id=' . $invoice->id); ?>" 
                                           class="button button-small"><?php _e('View', 'cfb-calculator'); ?></a>
                                        <a href="<?php echo admin_url('admin.php?page=cfb-calculator-invoices&action=edit&id=' . $invoice->id); ?>" 
                                           class="button button-small"><?php _e('Edit', 'cfb-calculator'); ?></a>
                                        <button class="button button-small cfb-generate-pdf" 
                                                data-invoice-id="<?php echo $invoice->id; ?>">
                                            <?php _e('PDF', 'cfb-calculator'); ?>
                                        </button>
                                        <button class="button button-small button-link-delete cfb-delete-invoice" 
                                                data-invoice-id="<?php echo $invoice->id; ?>">
                                            <?php _e('Delete', 'cfb-calculator'); ?>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        
        <style>
        .cfb-invoices {
            margin: 20px 0;
        }
        
        .cfb-empty-state {
            text-align: center;
            padding: 60px 20px;
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin-top: 20px;
        }
        
        .cfb-empty-icon .dashicons {
            font-size: 64px;
            color: #c3c4c7;
            margin-bottom: 20px;
        }
        
        .cfb-customer-info {
            line-height: 1.4;
        }
        
        .cfb-status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .cfb-status-draft {
            background: #f0f0f1;
            color: #646970;
        }
        
        .cfb-status-sent {
            background: #d1e7dd;
            color: #0f5132;
        }
        
        .cfb-status-paid {
            background: #cff4fc;
            color: #055160;
        }
        
        .cfb-status-overdue {
            background: #f8d7da;
            color: #721c24;
        }
        
        .cfb-invoice-actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .cfb-invoice-actions .button {
            font-size: 11px;
            padding: 2px 6px;
            height: auto;
        }
        </style>
        
        <script>
        jQuery(document).ready(function($) {
            // Handle PDF generation
            $('.cfb-generate-pdf').on('click', function() {
                const invoiceId = $(this).data('invoice-id');
                const button = $(this);
                
                button.prop('disabled', true).text('<?php _e('Generating...', 'cfb-calculator'); ?>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'cfb_generate_pdf',
                        invoice_id: invoiceId,
                        nonce: '<?php echo wp_create_nonce('cfb_invoice_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Download the PDF
                            window.open(response.data.pdf_url, '_blank');
                        } else {
                            alert(response.data || 'Failed to generate PDF');
                        }
                    },
                    error: function() {
                        alert('Network error occurred');
                    },
                    complete: function() {
                        button.prop('disabled', false).text('<?php _e('PDF', 'cfb-calculator'); ?>');
                    }
                });
            });
            
            // Handle invoice deletion
            $('.cfb-delete-invoice').on('click', function() {
                if (!confirm('<?php _e('Are you sure you want to delete this invoice?', 'cfb-calculator'); ?>')) {
                    return;
                }
                
                const invoiceId = $(this).data('invoice-id');
                const row = $(this).closest('tr');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'cfb_delete_invoice',
                        invoice_id: invoiceId,
                        nonce: '<?php echo wp_create_nonce('cfb_invoice_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            row.fadeOut(300, function() {
                                $(this).remove();
                            });
                        } else {
                            alert(response.data || 'Failed to delete invoice');
                        }
                    },
                    error: function() {
                        alert('Network error occurred');
                    }
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Render create invoice page
     */
    private function render_create_invoice_page() {
        $submission_id = isset($_GET['submission_id']) ? intval($_GET['submission_id']) : 0;
        $form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;
        $total = isset($_GET['total']) ? floatval($_GET['total']) : 0;
        
        $submission_data = null;
        $form_data = null;
        
        if ($submission_id) {
            $submission_data = $this->get_submission_data($submission_id);
        }
        
        if ($form_id) {
            $form_data = $this->get_form_data($form_id);
        }
        
        ?>
        <div class="wrap cfb-create-invoice">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-plus-alt"></span>
                <?php _e('Create New Invoice', 'cfb-calculator'); ?>
            </h1>
            <a href="<?php echo admin_url('admin.php?page=cfb-calculator-invoices'); ?>" class="page-title-action">
                <?php _e('Back to Invoices', 'cfb-calculator'); ?>
            </a>
            
            <form id="cfb-invoice-form" method="post">
                <?php wp_nonce_field('cfb_create_invoice', 'cfb_invoice_nonce'); ?>
                <input type="hidden" name="submission_id" value="<?php echo $submission_id; ?>">
                <input type="hidden" name="form_id" value="<?php echo $form_id; ?>">
                
                <div class="cfb-invoice-form-container">
                    <div class="cfb-invoice-form-main">
                        <div class="cfb-form-section">
                            <h2><?php _e('Customer Information', 'cfb-calculator'); ?></h2>
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="customer_name"><?php _e('Customer Name', 'cfb-calculator'); ?> *</label>
                                    </th>
                                    <td>
                                        <input type="text" id="customer_name" name="customer_name" class="regular-text" required>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="customer_email"><?php _e('Email Address', 'cfb-calculator'); ?> *</label>
                                    </th>
                                    <td>
                                        <input type="email" id="customer_email" name="customer_email" class="regular-text" required>
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="customer_phone"><?php _e('Phone Number', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="tel" id="customer_phone" name="customer_phone" class="regular-text">
                                    </td>
                                </tr>
                                <tr>
                                    <th scope="row">
                                        <label for="customer_address"><?php _e('Address', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <textarea id="customer_address" name="customer_address" rows="3" class="large-text"></textarea>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <?php
    }

    /**
     * Get invoices list
     */
    private function get_invoices($limit = 50, $offset = 0) {
        global $wpdb;

        return $wpdb->get_results($wpdb->prepare("
            SELECT i.*, f.name as form_name
            FROM {$wpdb->prefix}cfb_invoices i
            LEFT JOIN {$wpdb->prefix}cfb_forms f ON i.form_id = f.id
            ORDER BY i.created_at DESC
            LIMIT %d OFFSET %d
        ", $limit, $offset));
    }

    /**
     * Get submission data
     */
    private function get_submission_data($submission_id) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}cfb_form_submissions
            WHERE id = %d
        ", $submission_id));
    }

    /**
     * Get form data
     */
    private function get_form_data($form_id) {
        global $wpdb;

        return $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}cfb_forms
            WHERE id = %d
        ", $form_id));
    }

    /**
     * Generate invoice number
     */
    private function generate_invoice_number() {
        $prefix = get_option('cfb_invoice_prefix', 'INV');
        $year = date('Y');
        $month = date('m');

        global $wpdb;
        $count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*) FROM {$wpdb->prefix}cfb_invoices
            WHERE invoice_number LIKE %s
        ", $prefix . '-' . $year . $month . '%'));

        $number = str_pad($count + 1, 4, '0', STR_PAD_LEFT);

        return $prefix . '-' . $year . $month . '-' . $number;
    }

    /**
     * Format currency for display
     */
    private function format_currency($amount) {
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $currency_position = get_option('cfb_currency_position', 'left');
        $decimal_places = get_option('cfb_decimal_places', 2);

        $formatted = number_format($amount, $decimal_places);

        if ($currency_position === 'right') {
            return $formatted . ' ' . $currency_symbol;
        } else {
            return $currency_symbol . ' ' . $formatted;
        }
    }

    /**
     * AJAX save invoice handler
     */
    public function ajax_save_invoice() {
        // Log the request for debugging
        error_log('CFB Invoice: AJAX save invoice called');
        error_log('CFB Invoice: POST data: ' . print_r($_POST, true));

        // Check nonce - use the same nonce as calculator for frontend compatibility
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'cfb_calculator_nonce')) {
            error_log('CFB Invoice: Nonce verification failed');
            wp_send_json_error(__('Security check failed', 'cfb-calculator'));
        }

        // Allow both admin users and frontend users to create invoices
        // Frontend users can create invoices for their own calculations

        // Validate required fields
        if (empty($_POST['customer_name']) || empty($_POST['customer_email'])) {
            wp_send_json_error(__('Customer name and email are required', 'cfb-calculator'));
        }

        // Get form data if provided - handle both array and JSON string
        $form_data = '';
        if (isset($_POST['form_data'])) {
            if (is_array($_POST['form_data'])) {
                $form_data = wp_json_encode($_POST['form_data']);
            } elseif (is_string($_POST['form_data'])) {
                // If it's already JSON, validate and use it
                $decoded = json_decode($_POST['form_data'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $form_data = sanitize_textarea_field($_POST['form_data']);
                } else {
                    // If it's not valid JSON, treat as regular string
                    $form_data = wp_json_encode(array('data' => sanitize_textarea_field($_POST['form_data'])));
                }
            }
        }

        // Log form data for debugging
        error_log('CFB Invoice: Form data received: ' . print_r($_POST['form_data'], true));
        error_log('CFB Invoice: Form data to save: ' . $form_data);

        $invoice_data = array(
            'invoice_number' => $this->generate_invoice_number(),
            'form_id' => intval($_POST['form_id'] ?? 0),
            'submission_id' => !empty($_POST['submission_id']) ? intval($_POST['submission_id']) : null,
            'customer_name' => sanitize_text_field($_POST['customer_name']),
            'customer_email' => sanitize_email($_POST['customer_email']),
            'customer_phone' => sanitize_text_field($_POST['customer_phone'] ?? ''),
            'customer_address' => sanitize_textarea_field($_POST['customer_address'] ?? ''),
            'subtotal' => floatval($_POST['subtotal'] ?? 0),
            'tax_amount' => floatval($_POST['tax_amount'] ?? 0),
            'total_amount' => floatval($_POST['total_amount'] ?? 0),
            'currency' => get_option('cfb_currency_symbol', 'USD'),
            'status' => 'draft',
            'notes' => sanitize_textarea_field($_POST['notes'] ?? ''),
            'form_data' => $form_data
        );

        global $wpdb;

        // Log the invoice data being inserted
        error_log('CFB Invoice: Inserting invoice data: ' . print_r($invoice_data, true));

        // Check if form_data column exists, if not, remove it from the data
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$wpdb->prefix}cfb_invoices LIKE 'form_data'");
        if (empty($columns)) {
            // Column doesn't exist, remove form_data from insert
            unset($invoice_data['form_data']);
            $result = $wpdb->insert(
                $wpdb->prefix . 'cfb_invoices',
                $invoice_data,
                array('%s', '%d', '%d', '%s', '%s', '%s', '%s', '%f', '%f', '%f', '%s', '%s', '%s')
            );
        } else {
            // Column exists, insert with form_data
            $result = $wpdb->insert(
                $wpdb->prefix . 'cfb_invoices',
                $invoice_data,
                array('%s', '%d', '%d', '%s', '%s', '%s', '%s', '%f', '%f', '%f', '%s', '%s', '%s', '%s')
            );
        }

        if ($result === false) {
            error_log('CFB Invoice: Database insert failed: ' . $wpdb->last_error);
            wp_send_json_error(__('Database error: ', 'cfb-calculator') . $wpdb->last_error);
        }

        if ($result) {
            $invoice_id = $wpdb->insert_id;
            error_log('CFB Invoice: Invoice created successfully with ID: ' . $invoice_id);

            // Save invoice items if provided
            if (isset($_POST['items']) && is_array($_POST['items'])) {
                foreach ($_POST['items'] as $index => $item) {
                    $wpdb->insert(
                        $wpdb->prefix . 'cfb_invoice_items',
                        array(
                            'invoice_id' => $invoice_id,
                            'item_name' => sanitize_text_field($item['name']),
                            'item_description' => sanitize_textarea_field($item['description']),
                            'quantity' => floatval($item['quantity']),
                            'unit_price' => floatval($item['unit_price']),
                            'total_price' => floatval($item['total_price']),
                            'sort_order' => $index
                        ),
                        array('%d', '%s', '%s', '%f', '%f', '%f', '%d')
                    );
                }
            }

            wp_send_json_success(array(
                'message' => __('Invoice created successfully', 'cfb-calculator'),
                'invoice_id' => $invoice_id,
                'invoice_number' => $invoice_data['invoice_number']
            ));
        } else {
            error_log('CFB Invoice: Insert result was false but no error detected');
            wp_send_json_error(__('Failed to create invoice', 'cfb-calculator'));
        }
    }

    /**
     * AJAX delete invoice handler
     */
    public function ajax_delete_invoice() {
        check_ajax_referer('cfb_invoice_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }

        $invoice_id = intval($_POST['invoice_id']);

        if (!$invoice_id) {
            wp_send_json_error(__('Invalid invoice ID', 'cfb-calculator'));
        }

        global $wpdb;

        // Delete invoice items first
        $wpdb->delete(
            $wpdb->prefix . 'cfb_invoice_items',
            array('invoice_id' => $invoice_id),
            array('%d')
        );

        // Delete invoice
        $result = $wpdb->delete(
            $wpdb->prefix . 'cfb_invoices',
            array('id' => $invoice_id),
            array('%d')
        );

        if ($result) {
            wp_send_json_success(__('Invoice deleted successfully', 'cfb-calculator'));
        } else {
            wp_send_json_error(__('Failed to delete invoice', 'cfb-calculator'));
        }
    }

    /**
     * Render edit invoice page
     */
    private function render_edit_invoice_page() {
        // Implementation for edit page
        echo '<div class="wrap"><h1>Edit Invoice - Coming Soon</h1></div>';
    }

    /**
     * Render view invoice page
     */
    private function render_view_invoice_page() {
        // Implementation for view page
        echo '<div class="wrap"><h1>View Invoice - Coming Soon</h1></div>';
    }
}
