<?php
/**
 * CFB Calculator PDF Generator
 * Handles PDF invoice generation using TCPDF
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_PDF_Generator {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_cfb_generate_pdf', array($this, 'ajax_generate_pdf'));
        add_action('wp_ajax_nopriv_cfb_generate_pdf', array($this, 'ajax_generate_pdf'));
        
        // Load TCPDF library
        $this->load_tcpdf();
    }
    
    /**
     * Load TCPDF library
     */
    private function load_tcpdf() {
        if (!class_exists('TCPDF')) {
            // Load Composer autoloader first
            $autoloader_path = CFB_CALCULATOR_PLUGIN_PATH . 'vendor/autoload.php';
            if (file_exists($autoloader_path)) {
                require_once $autoloader_path;
            }

            // If still not available, check manual installation paths
            if (!class_exists('TCPDF')) {
                $tcpdf_paths = array(
                    CFB_CALCULATOR_PLUGIN_PATH . 'vendor/tecnickcom/tcpdf/tcpdf.php',
                    CFB_CALCULATOR_PLUGIN_PATH . 'lib/tcpdf/tcpdf.php',
                    ABSPATH . 'wp-content/plugins/tcpdf/tcpdf.php'
                );

                foreach ($tcpdf_paths as $path) {
                    if (file_exists($path)) {
                        require_once $path;
                        break;
                    }
                }
            }

            // If TCPDF is still not found, we'll create a simple fallback
            if (!class_exists('TCPDF')) {
                $this->create_tcpdf_fallback();
            }
        }
    }
    
    /**
     * Create TCPDF fallback for basic PDF generation
     */
    private function create_tcpdf_fallback() {
        // For now, we'll use WordPress's built-in functionality
        // In production, you should install TCPDF properly
    }
    
    /**
     * AJAX generate PDF handler
     */
    public function ajax_generate_pdf() {
        // Check nonce - use the same nonce as calculator for frontend compatibility
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'cfb_calculator_nonce')) {
            wp_send_json_error(__('Security check failed', 'cfb-calculator'));
        }

        $invoice_id = intval($_POST['invoice_id']);
        
        if (!$invoice_id) {
            wp_send_json_error(__('Invalid invoice ID', 'cfb-calculator'));
        }
        
        $invoice = $this->get_invoice_data($invoice_id);
        
        if (!$invoice) {
            wp_send_json_error(__('Invoice not found', 'cfb-calculator'));
        }
        
        try {
            $pdf_path = $this->generate_invoice_pdf($invoice);
            
            if ($pdf_path) {
                // Update invoice with PDF path
                global $wpdb;
                $wpdb->update(
                    $wpdb->prefix . 'cfb_invoices',
                    array('pdf_path' => $pdf_path),
                    array('id' => $invoice_id),
                    array('%s'),
                    array('%d')
                );
                
                wp_send_json_success(array(
                    'message' => __('PDF generated successfully', 'cfb-calculator'),
                    'pdf_url' => $this->get_pdf_url($pdf_path)
                ));
            } else {
                wp_send_json_error(__('Failed to generate PDF', 'cfb-calculator'));
            }
        } catch (Exception $e) {
            wp_send_json_error(__('PDF generation error: ', 'cfb-calculator') . $e->getMessage());
        }
    }
    
    /**
     * Get invoice data
     */
    public function get_invoice_data($invoice_id) {
        global $wpdb;
        
        $invoice = $wpdb->get_row($wpdb->prepare("
            SELECT i.*, f.name as form_name
            FROM {$wpdb->prefix}cfb_invoices i
            LEFT JOIN {$wpdb->prefix}cfb_forms f ON i.form_id = f.id
            WHERE i.id = %d
        ", $invoice_id));
        
        if ($invoice) {
            // Get invoice items
            $invoice->items = $wpdb->get_results($wpdb->prepare("
                SELECT * FROM {$wpdb->prefix}cfb_invoice_items
                WHERE invoice_id = %d
                ORDER BY sort_order
            ", $invoice_id));
        }
        
        return $invoice;
    }
    
    /**
     * Generate invoice PDF
     */
    public function generate_invoice_pdf($invoice) {
        if (!class_exists('TCPDF')) {
            return $this->generate_simple_pdf($invoice);
        }
        
        // Get PDF template
        $template = get_option('cfb_pdf_template', 'modern');
        
        switch ($template) {
            case 'classic':
                return $this->generate_classic_pdf($invoice);
            case 'minimal':
                return $this->generate_minimal_pdf($invoice);
            case 'modern':
            default:
                return $this->generate_modern_pdf($invoice);
        }
    }
    
    /**
     * Generate modern PDF template - PROFESSIONAL COMMERCIAL DESIGN
     */
    private function generate_modern_pdf($invoice) {
        try {
            // Get settings
            $rtl_support = get_option('cfb_pdf_rtl_support', 0);

            // Create new PDF document
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

            // Set document information
            $pdf->SetCreator('CFB Calculator');
            $pdf->SetAuthor(get_option('cfb_company_name', get_bloginfo('name')));
            $pdf->SetTitle('Invoice ' . $invoice->invoice_number);

            // Remove default header/footer
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Set margins
            $pdf->SetMargins(15, 15, 15);
            $pdf->SetAutoPageBreak(TRUE, 15);

            // Add a page
            $pdf->AddPage();

            // Set font with proper encoding
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 10, $rtl_support);

            // Build MODERN PDF content with professional design
            $this->add_modern_professional_header($pdf, $invoice);
            $this->add_modern_invoice_details($pdf, $invoice);
            $this->add_modern_customer_details($pdf, $invoice);
            $this->add_beautiful_form_fields_table($pdf, $invoice);
            $this->add_modern_invoice_totals($pdf, $invoice);
            $this->add_modern_professional_footer($pdf, $invoice);

            // Save PDF
            $upload_dir = wp_upload_dir();
            $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';

            if (!file_exists($pdf_dir)) {
                wp_mkdir_p($pdf_dir);
            }

            $filename = 'invoice-' . $invoice->invoice_number . '.pdf';
            $filepath = $pdf_dir . $filename;

            $pdf->Output($filepath, 'F');

            return 'cfb-invoices/' . $filename;

        } catch (Exception $e) {
            error_log('CFB PDF Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate classic PDF template - PROFESSIONAL TRADITIONAL DESIGN
     */
    private function generate_classic_pdf($invoice) {
        try {
            // Get settings
            $rtl_support = get_option('cfb_pdf_rtl_support', 0);

            // Create new PDF document
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

            // Set document information
            $pdf->SetCreator('CFB Calculator');
            $pdf->SetAuthor(get_option('cfb_company_name', get_bloginfo('name')));
            $pdf->SetTitle('Invoice ' . $invoice->invoice_number);

            // Remove default header/footer
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Set margins
            $pdf->SetMargins(20, 20, 20);
            $pdf->SetAutoPageBreak(TRUE, 20);

            // Add a page
            $pdf->AddPage();

            // Set font with proper encoding
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 10, $rtl_support);

            // Build CLASSIC PDF content with traditional business design
            $this->add_classic_header($pdf, $invoice);
            $this->add_classic_invoice_details($pdf, $invoice);
            $this->add_classic_customer_details($pdf, $invoice);
            $this->add_beautiful_form_fields_table($pdf, $invoice);
            $this->add_classic_totals($pdf, $invoice);
            $this->add_classic_footer($pdf, $invoice);

            // Save PDF
            $upload_dir = wp_upload_dir();
            $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';

            if (!file_exists($pdf_dir)) {
                wp_mkdir_p($pdf_dir);
            }

            $filename = 'invoice-' . $invoice->invoice_number . '.pdf';
            $filepath = $pdf_dir . $filename;

            $pdf->Output($filepath, 'F');

            return 'cfb-invoices/' . $filename;

        } catch (Exception $e) {
            error_log('CFB PDF Classic Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate minimal PDF template - CLEAN PROFESSIONAL DESIGN
     */
    private function generate_minimal_pdf($invoice) {
        try {
            // Get settings
            $rtl_support = get_option('cfb_pdf_rtl_support', 0);

            // Create new PDF document
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

            // Set document information
            $pdf->SetCreator('CFB Calculator');
            $pdf->SetAuthor(get_option('cfb_company_name', get_bloginfo('name')));
            $pdf->SetTitle('Invoice ' . $invoice->invoice_number);

            // Remove default header/footer
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);

            // Set margins - wider for minimal design
            $pdf->SetMargins(25, 25, 25);
            $pdf->SetAutoPageBreak(TRUE, 25);

            // Add a page
            $pdf->AddPage();

            // Set font with proper encoding
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 10, $rtl_support);

            // Build MINIMAL PDF content with clean design
            $this->add_minimal_header($pdf, $invoice);
            $this->add_minimal_details($pdf, $invoice);
            $this->add_clean_minimal_customer_info($pdf, $invoice);
            $this->add_beautiful_form_fields_table($pdf, $invoice);
            $this->add_minimal_totals($pdf, $invoice);
            $this->add_clean_minimal_footer($pdf, $invoice);

            // Save PDF
            $upload_dir = wp_upload_dir();
            $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';

            if (!file_exists($pdf_dir)) {
                wp_mkdir_p($pdf_dir);
            }

            $filename = 'invoice-' . $invoice->invoice_number . '.pdf';
            $filepath = $pdf_dir . $filename;

            $pdf->Output($filepath, 'F');

            return 'cfb-invoices/' . $filename;

        } catch (Exception $e) {
            error_log('CFB PDF Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Add company header to PDF
     */
    private function add_company_header($pdf, $invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));
        $company_website = get_option('cfb_company_website', get_site_url());
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Get color scheme colors
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Company logo - always place on top right
        $logo_url = get_option('cfb_company_logo', '');
        if ($logo_url) {
            // Convert URL to local path if it's a local file
            $logo_path = $this->url_to_path($logo_url);

            if ($logo_path && file_exists($logo_path)) {
                // Calculate logo position for top right
                $page_width = $pdf->getPageWidth();
                $logo_width = 40;
                $logo_x = $page_width - $logo_width - 20; // 20mm margin from right
                $pdf->Image($logo_path, $logo_x, 20, $logo_width, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
                $pdf->SetY(35);
            }
        }

        // Company name with color scheme
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 6, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $align = $rtl_support ? 'L' : 'R';
        $pdf->Cell(0, 8, $company_name, 0, 1, $align);

        // Company details
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        if ($company_address) {
            $pdf->Cell(0, 5, $company_address, 0, 1, $align);
        }
        if ($company_phone) {
            $phone_label = $rtl_support ? 'تلفن: ' : 'Phone: ';
            $pdf->Cell(0, 5, $phone_label . $company_phone, 0, 1, $align);
        }
        if ($company_email) {
            $email_label = $rtl_support ? 'ایمیل: ' : 'Email: ';
            $pdf->Cell(0, 5, $email_label . $company_email, 0, 1, $align);
        }
        if ($company_website) {
            $website_label = $rtl_support ? 'وب‌سایت: ' : 'Website: ';
            $pdf->Cell(0, 5, $website_label . $company_website, 0, 1, $align);
        }

        $pdf->Ln(10);
    }

    /**
     * Get color scheme colors
     */
    private function get_color_scheme_colors($scheme) {
        $colors = array();

        switch ($scheme) {
            case 'blue':
                $colors['primary'] = array('r' => 0, 'g' => 115, 'b' => 170);
                $colors['secondary'] = array('r' => 102, 'g' => 126, 'b' => 234);
                $colors['accent'] = array('r' => 51, 'g' => 51, 'b' => 51);
                break;
            case 'green':
                $colors['primary'] = array('r' => 86, 'g' => 171, 'b' => 47);
                $colors['secondary'] = array('r' => 168, 'g' => 230, 'b' => 207);
                $colors['accent'] = array('r' => 51, 'g' => 51, 'b' => 51);
                break;
            case 'gray':
                $colors['primary'] = array('r' => 44, 'g' => 62, 'b' => 80);
                $colors['secondary'] = array('r' => 189, 'g' => 195, 'b' => 199);
                $colors['accent'] = array('r' => 51, 'g' => 51, 'b' => 51);
                break;
            default:
                $colors['primary'] = array('r' => 0, 'g' => 115, 'b' => 170);
                $colors['secondary'] = array('r' => 102, 'g' => 102, 'b' => 102);
                $colors['accent'] = array('r' => 51, 'g' => 51, 'b' => 51);
                break;
        }

        return $colors;
    }

    /**
     * Convert URL to local file path
     */
    private function url_to_path($url) {
        $upload_dir = wp_upload_dir();

        // Check if it's a local URL
        if (strpos($url, $upload_dir['baseurl']) === 0) {
            return str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $url);
        }

        // If it's an external URL, try to download it temporarily
        $temp_file = download_url($url);
        if (!is_wp_error($temp_file)) {
            return $temp_file;
        }

        return false;
    }

    /**
     * Set PDF font with Persian font support
     */
    private function set_pdf_font($pdf, $font_family, $font_size, $rtl_support = false, $style = '') {
        // Persian fonts that require special handling
        $persian_fonts = ['xyekan', 'xnazanin', 'xzar'];

        try {
            if (in_array($font_family, $persian_fonts)) {
                // Check if Persian font files exist
                $font_path = K_PATH_FONTS . $font_family . '.php';

                if (file_exists($font_path)) {
                    $pdf->SetFont($font_family, $style, $font_size);
                } else {
                    // Fallback to DejaVu Sans for Persian text
                    error_log("CFB PDF: Persian font $font_family not found, falling back to DejaVu Sans");
                    $pdf->SetFont('dejavusans', $style, $font_size);
                }
            } elseif ($rtl_support || $font_family === 'dejavusans') {
                // Use DejaVu Sans for RTL support
                $pdf->SetFont('dejavusans', $style, $font_size);
            } else {
                // Use standard fonts
                $pdf->SetFont($font_family, $style, $font_size);
            }
        } catch (Exception $e) {
            // Fallback to Helvetica if font loading fails
            error_log("CFB PDF: Font loading failed for $font_family, falling back to Helvetica: " . $e->getMessage());
            $pdf->SetFont('helvetica', $style, $font_size);
        }
    }

    /**
     * Check if Persian fonts are available
     */
    private function check_persian_fonts() {
        $persian_fonts = ['xyekan', 'xnazanin', 'xzar'];
        $available_fonts = [];

        foreach ($persian_fonts as $font) {
            $font_path = K_PATH_FONTS . $font . '.php';
            if (file_exists($font_path)) {
                $available_fonts[] = $font;
            }
        }

        return $available_fonts;
    }

    /**
     * Convert numbers to Farsi digits with RTL support
     */
    private function convert_to_farsi_digits($text) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);

        if (!$rtl_support || !get_option('cfb_convert_to_farsi_digits', 0)) {
            return $text;
        }

        $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $farsi_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];

        // Convert digits
        $converted = str_replace($english_digits, $farsi_digits, $text);

        // For RTL, we need to handle number direction properly
        // Numbers should read left-to-right even in RTL context
        if ($rtl_support && preg_match('/[\d۰-۹]/', $converted)) {
            // Add LTR mark before numbers to ensure correct direction
            $converted = preg_replace('/([۰-۹,.\s]+)/', "\u{202D}$1\u{202C}", $converted);
        }

        return $converted;
    }

    /**
     * Format currency with proper RTL and Farsi digit support
     */
    private function format_currency_farsi($amount) {
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $currency_position = get_option('cfb_currency_position', 'left');
        $decimal_places = intval(get_option('cfb_decimal_places', 2));
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);

        // Format number with proper thousands separator
        $formatted_amount = number_format($amount, $decimal_places);

        // For RTL mode
        if ($rtl_support) {
            // Convert to Farsi digits
            $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            $farsi_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
            $formatted_amount = str_replace($english_digits, $farsi_digits, $formatted_amount);

            // For RTL, put currency after the number
            return $formatted_amount . ' ' . $currency_symbol;
        } else {
            // For LTR, follow the currency position setting
            if ($currency_position === 'right') {
                return $formatted_amount . ' ' . $currency_symbol;
            } else {
                return $currency_symbol . ' ' . $formatted_amount;
            }
        }
    }

    /**
     * Add invoice details to PDF
     */
    private function add_invoice_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Invoice title - use custom title from settings
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 14, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $custom_title = get_option('cfb_invoice_title', '');
        if (empty($custom_title)) {
            $invoice_title = $rtl_support ? 'فاکتور' : 'INVOICE';
        } else {
            $invoice_title = $custom_title;
        }
        $align = $rtl_support ? 'R' : 'L';
        $pdf->Cell(0, 12, $invoice_title, 0, 1, $align);

        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        // Invoice number and date
        $invoice_number_label = $rtl_support ? 'شماره فاکتور:' : 'Invoice Number:';
        $invoice_date_label = $rtl_support ? 'تاریخ فاکتور:' : 'Invoice Date:';

        $pdf->Cell(40, 6, $invoice_number_label, 0, 0, $align);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support, 'B');
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);
        $pdf->Cell(0, 6, $this->convert_to_farsi_digits($invoice->invoice_number), 0, 1, $align);

        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->Cell(40, 6, $invoice_date_label, 0, 0, $align);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support, 'B');
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        // Format date based on RTL support
        $date_format = $rtl_support ? 'Y/m/d' : 'F j, Y';
        $formatted_date = date($date_format, strtotime($invoice->created_at));
        $pdf->Cell(0, 6, $this->convert_to_farsi_digits($formatted_date), 0, 1, $align);

        $pdf->Ln(5);
    }
    
    /**
     * Add customer details to PDF
     */
    private function add_customer_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size + 2);
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

        $bill_to_label = $rtl_support ? 'صورتحساب برای:' : 'Bill To:';
        $align = $rtl_support ? 'R' : 'L';
        $pdf->Cell(0, 8, $bill_to_label, 0, 1, $align);

        $pdf->SetFont($pdf->getFontFamily(), '', $font_size);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        $pdf->Cell(0, 5, $invoice->customer_name, 0, 1, $align);
        if ($invoice->customer_email) {
            $pdf->Cell(0, 5, $invoice->customer_email, 0, 1, $align);
        }
        if ($invoice->customer_phone) {
            $pdf->Cell(0, 5, $invoice->customer_phone, 0, 1, $align);
        }
        if ($invoice->customer_address) {
            $pdf->MultiCell(0, 5, $invoice->customer_address, 0, $align);
        }

        $pdf->Ln(10);
    }
    
    /**
     * Add invoice items table to PDF
     */
    private function add_invoice_items_table($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Table header
        $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size);
        $pdf->SetFillColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->SetTextColor(255, 255, 255);

        // RTL table headers
        if ($rtl_support) {
            $headers = array(
                'جمع کل' => 35,
                'قیمت واحد' => 30,
                'تعداد' => 25,
                'شرح' => 80
            );
            $aligns = array('R', 'R', 'C', 'R');
        } else {
            $headers = array(
                'Description' => 80,
                'Quantity' => 25,
                'Unit Price' => 30,
                'Total' => 35
            );
            $aligns = array('L', 'C', 'R', 'R');
        }

        $i = 0;
        foreach ($headers as $header => $width) {
            $pdf->Cell($width, 8, $header, 1, 0, $aligns[$i], true);
            $i++;
        }
        $pdf->Ln();

        // Table content
        $pdf->SetFont($pdf->getFontFamily(), '', $font_size - 1);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        if (!empty($invoice->items)) {
            foreach ($invoice->items as $item) {
                if ($rtl_support) {
                    $pdf->Cell(35, 6, $this->format_currency_farsi($item->total_price), 1, 0, 'R');
                    $pdf->Cell(30, 6, $this->format_currency_farsi($item->unit_price), 1, 0, 'R');
                    $pdf->Cell(25, 6, $this->convert_to_farsi_digits(number_format($item->quantity, 2)), 1, 0, 'C');
                    $pdf->Cell(80, 6, $item->item_name, 1, 1, 'R');
                } else {
                    $pdf->Cell(80, 6, $item->item_name, 1, 0, 'L');
                    $pdf->Cell(25, 6, $this->convert_to_farsi_digits(number_format($item->quantity, 2)), 1, 0, 'C');
                    $pdf->Cell(30, 6, $this->format_currency_farsi($item->unit_price), 1, 0, 'R');
                    $pdf->Cell(35, 6, $this->format_currency_farsi($item->total_price), 1, 1, 'R');
                }
            }
        } else {
            // If no items, show the form calculation as a single item
            $calculation_label = $rtl_support ? 'محاسبه ' . $invoice->form_name : $invoice->form_name . ' Calculation';

            if ($rtl_support) {
                $pdf->Cell(35, 6, $this->format_currency_farsi($invoice->subtotal), 1, 0, 'R');
                $pdf->Cell(30, 6, $this->format_currency_farsi($invoice->subtotal), 1, 0, 'R');
                $pdf->Cell(25, 6, $this->convert_to_farsi_digits('1'), 1, 0, 'C');
                $pdf->Cell(80, 6, $calculation_label, 1, 1, 'R');
            } else {
                $pdf->Cell(80, 6, $calculation_label, 1, 0, 'L');
                $pdf->Cell(25, 6, $this->convert_to_farsi_digits('1'), 1, 0, 'C');
                $pdf->Cell(30, 6, $this->format_currency_farsi($invoice->subtotal), 1, 0, 'R');
                $pdf->Cell(35, 6, $this->format_currency_farsi($invoice->subtotal), 1, 1, 'R');
            }
        }

        $pdf->Ln(5);
    }
    
    /**
     * Add invoice totals to PDF
     */
    private function add_invoice_totals($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        $pdf->SetFont($pdf->getFontFamily(), '', $font_size);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        // Labels based on RTL support
        $subtotal_label = $rtl_support ? 'جمع جزء:' : 'Subtotal:';
        $tax_label = $rtl_support ? 'مالیات:' : 'Tax:';
        $total_label = $rtl_support ? 'جمع کل:' : 'Total:';

        // Subtotal
        if ($rtl_support) {
            $pdf->Cell(35, 6, $this->format_currency_farsi($invoice->subtotal), 0, 0, 'L');
            $pdf->Cell(30, 6, $subtotal_label, 0, 1, 'L');
        } else {
            $pdf->Cell(135, 6, '', 0, 0);
            $pdf->Cell(30, 6, $subtotal_label, 0, 0, 'R');
            $pdf->Cell(35, 6, $this->format_currency_farsi($invoice->subtotal), 0, 1, 'R');
        }

        // Tax
        if ($invoice->tax_amount > 0) {
            if ($rtl_support) {
                $pdf->Cell(35, 6, $this->format_currency_farsi($invoice->tax_amount), 0, 0, 'L');
                $pdf->Cell(30, 6, $tax_label, 0, 1, 'L');
            } else {
                $pdf->Cell(135, 6, '', 0, 0);
                $pdf->Cell(30, 6, $tax_label, 0, 0, 'R');
                $pdf->Cell(35, 6, $this->format_currency_farsi($invoice->tax_amount), 0, 1, 'R');
            }
        }

        // Total
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

        if ($rtl_support) {
            $pdf->Cell(35, 8, $this->format_currency_farsi($invoice->total_amount), 0, 0, 'L');
            $pdf->Cell(30, 8, $total_label, 0, 1, 'L');
        } else {
            $pdf->Cell(135, 8, '', 0, 0);
            $pdf->Cell(30, 8, $total_label, 0, 0, 'R');
            $pdf->Cell(35, 8, $this->format_currency_farsi($invoice->total_amount), 0, 1, 'R');
        }

        $pdf->Ln(10);
    }
    
    /**
     * Add invoice footer to PDF
     */
    private function add_invoice_footer($pdf, $invoice) {
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        $align = $rtl_support ? 'R' : 'L';

        if ($payment_terms) {
            $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size);
            $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

            $payment_terms_label = $rtl_support ? 'شرایط پرداخت:' : 'Payment Terms:';
            $pdf->Cell(0, 6, $payment_terms_label, 0, 1, $align);

            $pdf->SetFont($pdf->getFontFamily(), '', $font_size - 1);
            $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
            $pdf->MultiCell(0, 5, $payment_terms, 0, $align);
        }

        if ($invoice->notes) {
            $pdf->Ln(5);
            $pdf->SetFont($pdf->getFontFamily(), 'B', $font_size);
            $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

            $notes_label = $rtl_support ? 'یادداشت‌ها:' : 'Notes:';
            $pdf->Cell(0, 6, $notes_label, 0, 1, $align);

            $pdf->SetFont($pdf->getFontFamily(), '', $font_size - 1);
            $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
            $pdf->MultiCell(0, 5, $invoice->notes, 0, $align);
        }

        // Add a professional footer with company info
        $pdf->Ln(15);
        $pdf->SetFont($pdf->getFontFamily(), 'I', $font_size - 2);
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        $footer_text = $rtl_support ?
            'این فاکتور توسط ' . get_option('cfb_company_name', get_bloginfo('name')) . ' تولید شده است.' :
            'This invoice was generated by ' . get_option('cfb_company_name', get_bloginfo('name')) . '.';

        $pdf->Cell(0, 4, $footer_text, 0, 1, 'C');

        $generation_date = $rtl_support ?
            'تاریخ تولید: ' . date('Y/m/d H:i') :
            'Generated on: ' . date('F j, Y \a\t g:i A');

        $pdf->Cell(0, 4, $generation_date, 0, 1, 'C');
    }





    /**
     * Add beautiful form fields table - Professional Design
     */
    private function add_beautiful_form_fields_table($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Get form data from invoice with fallback
        $form_data = array();
        if (isset($invoice->form_data) && !empty($invoice->form_data)) {
            $form_data = json_decode($invoice->form_data, true);
        }

        // If no form data, try to get from submission if available
        if (empty($form_data) && isset($invoice->submission_id) && $invoice->submission_id) {
            global $wpdb;
            $submission = $wpdb->get_row($wpdb->prepare("
                SELECT submission_data FROM {$wpdb->prefix}cfb_form_submissions
                WHERE id = %d
            ", $invoice->submission_id));

            if ($submission && $submission->submission_data) {
                $form_data = json_decode($submission->submission_data, true);
            }
        }

        // Log for debugging
        error_log('CFB PDF: Form data retrieved: ' . print_r($form_data, true));
        error_log('CFB PDF: Invoice object: ' . print_r($invoice, true));

        if (empty($form_data)) {
            // Show a message instead of hiding the section
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support, 'I');
            $pdf->SetTextColor(120, 120, 120);

            $no_data_msg = $rtl_support ? 'هیچ داده فرمی موجود نیست' : 'No form data available';
            $pdf->Cell(0, 6, $no_data_msg, 0, 1, $rtl_support ? 'R' : 'L');
            $pdf->Ln(10);
            return;
        }

        // Section title
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support, 'B');
        $pdf->SetTextColor(60, 60, 60);

        $form_fields_title = $rtl_support ? 'جزئیات فرم محاسبه' : 'Form Calculation Details';

        if ($rtl_support) {
            $pdf->Cell(0, 8, $form_fields_title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 8, $form_fields_title, 0, 1, 'L');
        }

        $pdf->Ln(3);

        // Clean professional table
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support, 'B');
        $pdf->SetFillColor(245, 245, 245);
        $pdf->SetTextColor(80, 80, 80);
        $pdf->SetDrawColor(200, 200, 200);
        $pdf->SetLineWidth(0.2);

        // Table headers
        if ($rtl_support) {
            $pdf->Cell(90, 7, 'مقدار', 1, 0, 'R', true);
            $pdf->Cell(80, 7, 'فیلد', 1, 1, 'R', true);
        } else {
            $pdf->Cell(80, 7, 'Field', 1, 0, 'L', true);
            $pdf->Cell(90, 7, 'Value', 1, 1, 'L', true);
        }

        // Table rows
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(60, 60, 60);

        $row_count = 0;
        foreach ($form_data as $field_name => $field_value) {
            if (is_array($field_value)) {
                $field_value = implode(', ', $field_value);
            }

            // Clean field name
            $clean_field_name = ucwords(str_replace(['_', '-'], ' ', $field_name));

            // Alternating row colors
            if ($row_count % 2 == 0) {
                $pdf->SetFillColor(252, 252, 252);
            } else {
                $pdf->SetFillColor(255, 255, 255);
            }

            // Table row
            if ($rtl_support) {
                $pdf->Cell(90, 6, $this->convert_to_farsi_digits($field_value), 1, 0, 'R', true);
                $pdf->Cell(80, 6, $this->convert_to_farsi_digits($clean_field_name), 1, 1, 'R', true);
            } else {
                $pdf->Cell(80, 6, $this->convert_to_farsi_digits($clean_field_name), 1, 0, 'L', true);
                $pdf->Cell(90, 6, $this->convert_to_farsi_digits($field_value), 1, 1, 'L', true);
            }

            $row_count++;
        }

        $pdf->Ln(10);
    }

    /**
     * Add classic header - Enhanced Beautiful Design
     */
    private function add_classic_header($pdf, $invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $colors = $this->get_color_scheme_colors(get_option('cfb_pdf_color_scheme', 'blue'));

        // Elegant double border frame
        $pdf->SetDrawColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetLineWidth(1.0);
        $pdf->Rect(15, 15, 180, 50);

        $pdf->SetLineWidth(0.3);
        $pdf->Rect(18, 18, 174, 44);

        // Decorative corner elements
        $pdf->SetFillColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->Rect(15, 15, 8, 8, 'F');
        $pdf->Rect(187, 15, 8, 8, 'F');
        $pdf->Rect(15, 57, 8, 8, 'F');
        $pdf->Rect(187, 57, 8, 8, 'F');

        // Company logo with elegant frame
        $logo_url = get_option('cfb_company_logo', '');
        if ($logo_url) {
            $logo_path = $this->url_to_path($logo_url);
            if ($logo_path && file_exists($logo_path)) {
                // Logo background frame
                $pdf->SetFillColor(255, 255, 255);
                $pdf->SetDrawColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
                $pdf->SetLineWidth(0.5);
                $pdf->RoundedRect(145, 20, 45, 35, 3, '1111', 'DF');

                $pdf->Image($logo_path, 150, 25, 35, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
            }
        }

        // Company name with elegant typography
        $pdf->SetXY(25, 22);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 6, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->Cell(0, 10, $company_name, 0, 1, 'L');

        // Decorative line under company name
        $pdf->SetDrawColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->SetLineWidth(0.5);
        $pdf->Line(25, $pdf->GetY() + 1, 25 + (strlen($company_name) * 2), $pdf->GetY() + 1);

        // Company details in elegant format
        $pdf->SetXY(25, 35);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        $contact_details = array();
        if ($company_address) $contact_details[] = $company_address;
        if ($company_phone) $contact_details[] = 'Tel: ' . $company_phone;
        if ($company_email) $contact_details[] = 'Email: ' . $company_email;

        foreach ($contact_details as $detail) {
            $pdf->Cell(0, 5, $detail, 0, 1, 'L');
            $pdf->SetX(25);
        }

        $pdf->SetY(75);
    }

    /**
     * Add classic invoice details - Enhanced Beautiful Design
     */
    private function add_classic_invoice_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $colors = $this->get_color_scheme_colors(get_option('cfb_pdf_color_scheme', 'blue'));

        // Elegant invoice title banner
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 12, $rtl_support, 'B');
        $pdf->SetTextColor(255, 255, 255);

        $custom_title = get_option('cfb_invoice_title', '');
        $invoice_title = !empty($custom_title) ? $custom_title : ($rtl_support ? 'فاکتور' : 'INVOICE');

        // Gradient-style banner
        $pdf->SetFillColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetDrawColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetLineWidth(0.5);
        $pdf->RoundedRect(15, $pdf->GetY(), 180, 15, 2, '1111', 'DF');

        $pdf->SetXY(15, $pdf->GetY() + 3);
        $pdf->Cell(180, 9, $invoice_title, 0, 1, 'C');
        $pdf->Ln(8);

        // Elegant invoice details card
        $pdf->SetFillColor(248, 250, 252);
        $pdf->SetDrawColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->SetLineWidth(0.3);
        $pdf->RoundedRect(15, $pdf->GetY(), 180, 20, 2, '1111', 'DF');

        // Invoice details in elegant two-column layout
        $pdf->SetXY(20, $pdf->GetY() + 5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

        // Left column
        $pdf->Cell(80, 5, 'Invoice Number:', 0, 0, 'L');
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);
        $pdf->Cell(0, 5, $this->convert_to_farsi_digits($invoice->invoice_number), 0, 1, 'L');

        // Right column (Date)
        $pdf->SetX(20);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->Cell(80, 5, 'Invoice Date:', 0, 0, 'L');
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);
        $pdf->Cell(0, 5, $this->convert_to_farsi_digits(date('F j, Y', strtotime($invoice->created_at))), 0, 1, 'L');

        $pdf->Ln(10);
    }

    /**
     * Add classic customer details - Enhanced Beautiful Design
     */
    private function add_classic_customer_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $colors = $this->get_color_scheme_colors(get_option('cfb_pdf_color_scheme', 'blue'));

        // Elegant customer section with decorative header
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support, 'B');
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFillColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetDrawColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetLineWidth(0.5);

        // Header with rounded corners
        $pdf->RoundedRect(15, $pdf->GetY(), 180, 10, 2, '1100', 'DF');
        $pdf->SetXY(20, $pdf->GetY() + 2);
        $pdf->Cell(0, 6, 'Bill To:', 0, 1, 'L');

        // Customer details container
        $pdf->SetFillColor(255, 255, 255);
        $pdf->SetDrawColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->SetLineWidth(0.3);
        $pdf->Rect(15, $pdf->GetY(), 180, 25, 'DF');

        // Customer name with emphasis
        $pdf->SetXY(20, $pdf->GetY() + 3);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->Cell(0, 6, $invoice->customer_name, 0, 1, 'L');

        // Customer contact details in elegant format
        $pdf->SetX(20);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        $contact_info = array();
        if ($invoice->customer_email) $contact_info[] = '✉ ' . $invoice->customer_email;
        if ($invoice->customer_phone) $contact_info[] = '☎ ' . $invoice->customer_phone;

        foreach ($contact_info as $info) {
            $pdf->Cell(0, 5, $info, 0, 1, 'L');
            $pdf->SetX(20);
        }

        if ($invoice->customer_address) {
            $pdf->SetX(20);
            $pdf->Cell(0, 5, '📍 ' . $invoice->customer_address, 0, 1, 'L');
        }

        $pdf->Ln(8);
    }

    /**
     * Add classic totals - Enhanced Beautiful Design
     */
    private function add_classic_totals($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $colors = $this->get_color_scheme_colors(get_option('cfb_pdf_color_scheme', 'blue'));

        // Elegant totals section with decorative frame
        $pdf->SetDrawColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetLineWidth(0.5);
        $pdf->SetFillColor(248, 250, 252);

        // Totals container with rounded corners
        $pdf->RoundedRect(110, $pdf->GetY(), 85, 30, 3, '1111', 'DF');

        $start_y = $pdf->GetY();

        // Subtotal row
        $pdf->SetXY(115, $start_y + 5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);
        $pdf->Cell(40, 6, 'Subtotal:', 0, 0, 'L');
        $pdf->Cell(35, 6, $this->format_currency_farsi($invoice->subtotal), 0, 1, 'R');

        // Tax row (if applicable)
        if ($invoice->tax_amount > 0) {
            $pdf->SetX(115);
            $pdf->Cell(40, 6, 'Tax:', 0, 0, 'L');
            $pdf->Cell(35, 6, $this->format_currency_farsi($invoice->tax_amount), 0, 1, 'R');
        }

        // Decorative line before total
        $pdf->SetDrawColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->SetLineWidth(0.3);
        $pdf->Line(115, $pdf->GetY() + 2, 190, $pdf->GetY() + 2);

        // Total row with emphasis
        $pdf->SetXY(115, $pdf->GetY() + 5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 3, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->Cell(40, 8, 'TOTAL:', 0, 0, 'L');
        $pdf->Cell(35, 8, $this->format_currency_farsi($invoice->total_amount), 0, 1, 'R');

        $pdf->Ln(15);
    }

    /**
     * Add classic footer - Enhanced Beautiful Design
     */
    private function add_classic_footer($pdf, $invoice) {
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $colors = $this->get_color_scheme_colors(get_option('cfb_pdf_color_scheme', 'blue'));

        // Payment terms in elegant bordered section
        if ($payment_terms) {
            $pdf->SetDrawColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
            $pdf->SetLineWidth(0.5);
            $pdf->SetFillColor(248, 250, 252);
            $pdf->RoundedRect(15, $pdf->GetY(), 180, 25, 3, '1111', 'DF');

            // Payment terms header
            $pdf->SetXY(20, $pdf->GetY() + 3);
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support, 'B');
            $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
            $pdf->Cell(0, 6, '💳 Payment Terms & Conditions', 0, 1, 'L');

            // Payment terms content
            $pdf->SetX(20);
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
            $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);
            $pdf->MultiCell(170, 5, $payment_terms, 0, 'L');
        }

        // Elegant footer with company branding
        $pdf->SetY($pdf->getPageHeight() - 25);

        // Decorative line
        $pdf->SetDrawColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);
        $pdf->SetLineWidth(0.3);
        $pdf->Line(15, $pdf->GetY(), 195, $pdf->GetY());

        $pdf->Ln(5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support, 'I');
        $pdf->SetTextColor($colors['secondary']['r'], $colors['secondary']['g'], $colors['secondary']['b']);

        $footer_text = $rtl_support ?
            'این فاکتور توسط ' . get_option('cfb_company_name', get_bloginfo('name')) . ' با افتخار تولید شده است.' :
            'This invoice was professionally generated by ' . get_option('cfb_company_name', get_bloginfo('name'));

        $pdf->Cell(0, 4, $footer_text, 0, 1, 'C');

        $generation_date = $rtl_support ?
            'تاریخ و زمان تولید: ' . $this->convert_to_farsi_digits(date('Y/m/d H:i')) :
            'Generated on: ' . date('F j, Y \a\t g:i A');

        $pdf->Cell(0, 4, $generation_date, 0, 1, 'C');
    }

    /**
     * Add modern minimal header - Clean Professional Design
     */
    private function add_modern_minimal_header($pdf, $invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Clean minimal header with CORRECT logo placement
        $logo_url = get_option('cfb_company_logo', '');
        if ($logo_url) {
            $logo_path = $this->url_to_path($logo_url);
            if ($logo_path && file_exists($logo_path)) {
                if ($rtl_support) {
                    // RTL: Logo on RIGHT side
                    $pdf->Image($logo_path, $pdf->getPageWidth() - 55, 25, 30, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
                } else {
                    // LTR: Logo on LEFT side
                    $pdf->Image($logo_path, 25, 25, 30, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
                }
            }
        }

        // Company name
        if ($rtl_support) {
            $pdf->SetXY(25, 25);
        } else {
            $pdf->SetXY($logo_url ? 65 : 25, 25);
        }

        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 4, $rtl_support, 'B');
        $pdf->SetTextColor(40, 40, 40);
        $align = $rtl_support ? 'R' : 'L';
        $pdf->Cell(0, 8, $company_name, 0, 1, $align);

        // Company details
        if ($rtl_support) {
            $pdf->SetX($pdf->getPageWidth() - 45);
        } else {
            $pdf->SetX(25);
        }

        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(100, 100, 100);

        $contact_info = array();
        if ($company_email) $contact_info[] = $company_email;
        if ($company_phone) $contact_info[] = $company_phone;

        if (!empty($contact_info)) {
            $pdf->Cell(0, 5, implode(' | ', $contact_info), 0, 1, $align);
        }

        // Clean separator line
        $pdf->Ln(5);
        $pdf->SetDrawColor(220, 220, 220);
        $pdf->SetLineWidth(0.3);
        $pdf->Line(25, $pdf->GetY(), $pdf->getPageWidth() - 25, $pdf->GetY());
        $pdf->Ln(10);
    }

    /**
     * Add modern minimal invoice details
     */
    private function add_modern_minimal_invoice_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Invoice title
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 8, $rtl_support, 'B');
        $pdf->SetTextColor(40, 40, 40);

        $custom_title = get_option('cfb_invoice_title', '');
        $invoice_title = !empty($custom_title) ? $custom_title : ($rtl_support ? 'فاکتور' : 'INVOICE');

        if ($rtl_support) {
            $pdf->Cell(0, 12, $invoice_title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 12, $invoice_title, 0, 1, 'L');
        }

        // Invoice details in one clean line
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor(120, 120, 120);

        $invoice_info = $rtl_support ?
            'شماره: ' . $this->convert_to_farsi_digits($invoice->invoice_number) . ' | تاریخ: ' . $this->convert_to_farsi_digits(date('Y/m/d', strtotime($invoice->created_at))) :
            'Invoice #' . $this->convert_to_farsi_digits($invoice->invoice_number) . ' | Date: ' . $this->convert_to_farsi_digits(date('M j, Y', strtotime($invoice->created_at)));

        if ($rtl_support) {
            $pdf->Cell(0, 6, $invoice_info, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 6, $invoice_info, 0, 1, 'L');
        }

        $pdf->Ln(8);
    }

    /**
     * Add modern minimal customer details
     */
    private function add_modern_minimal_customer_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Customer section title
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support, 'B');
        $pdf->SetTextColor(80, 80, 80);

        $bill_to_label = $rtl_support ? 'صورتحساب برای:' : 'Bill To:';

        if ($rtl_support) {
            $pdf->Cell(0, 6, $bill_to_label, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 6, $bill_to_label, 0, 1, 'L');
        }

        // Customer name
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support, 'B');
        $pdf->SetTextColor(40, 40, 40);

        if ($rtl_support) {
            $pdf->Cell(0, 6, $invoice->customer_name, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 6, $invoice->customer_name, 0, 1, 'L');
        }

        // Customer contact info
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(120, 120, 120);

        $customer_info = array();
        if ($invoice->customer_email) $customer_info[] = $invoice->customer_email;
        if ($invoice->customer_phone) $customer_info[] = $invoice->customer_phone;

        if (!empty($customer_info)) {
            if ($rtl_support) {
                $pdf->Cell(0, 5, implode(' | ', $customer_info), 0, 1, 'R');
            } else {
                $pdf->Cell(0, 5, implode(' | ', $customer_info), 0, 1, 'L');
            }
        }

        $pdf->Ln(8);
    }

    /**
     * Add modern minimal calculation summary
     */
    private function add_modern_minimal_calculation_summary($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Summary section
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support, 'B');
        $pdf->SetTextColor(60, 60, 60);

        $summary_title = $rtl_support ? 'خلاصه محاسبه' : 'Calculation Summary';

        if ($rtl_support) {
            $pdf->Cell(0, 8, $summary_title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 8, $summary_title, 0, 1, 'L');
        }

        $pdf->Ln(3);

        // Clean summary table
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor(80, 80, 80);
        $pdf->SetDrawColor(200, 200, 200);
        $pdf->SetLineWidth(0.2);

        // Calculation item
        $calculation_label = $rtl_support ? 'محاسبه ' . $invoice->form_name : $invoice->form_name . ' Calculation';

        if ($rtl_support) {
            $pdf->Cell(120, 7, $this->format_currency_farsi($invoice->subtotal), 1, 0, 'L');
            $pdf->Cell(50, 7, $calculation_label, 1, 1, 'R');
        } else {
            $pdf->Cell(120, 7, $calculation_label, 1, 0, 'L');
            $pdf->Cell(50, 7, $this->format_currency_farsi($invoice->subtotal), 1, 1, 'R');
        }

        // Tax if applicable
        if ($invoice->tax_amount > 0) {
            $tax_label = $rtl_support ? 'مالیات' : 'Tax';

            if ($rtl_support) {
                $pdf->Cell(120, 7, $this->format_currency_farsi($invoice->tax_amount), 1, 0, 'L');
                $pdf->Cell(50, 7, $tax_label, 1, 1, 'R');
            } else {
                $pdf->Cell(120, 7, $tax_label, 1, 0, 'L');
                $pdf->Cell(50, 7, $this->format_currency_farsi($invoice->tax_amount), 1, 1, 'R');
            }
        }

        // Total
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support, 'B');
        $pdf->SetTextColor(40, 40, 40);
        $pdf->SetFillColor(245, 245, 245);

        $total_label = $rtl_support ? 'جمع کل' : 'Total';

        if ($rtl_support) {
            $pdf->Cell(120, 8, $this->format_currency_farsi($invoice->total_amount), 1, 0, 'L', true);
            $pdf->Cell(50, 8, $total_label, 1, 1, 'R', true);
        } else {
            $pdf->Cell(120, 8, $total_label, 1, 0, 'L', true);
            $pdf->Cell(50, 8, $this->format_currency_farsi($invoice->total_amount), 1, 1, 'R', true);
        }

        $pdf->Ln(10);
    }

    /**
     * Add modern minimal footer
     */
    private function add_modern_minimal_footer($pdf, $invoice) {
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Payment terms if available
        if ($payment_terms) {
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
            $pdf->SetTextColor(120, 120, 120);

            $terms_label = $rtl_support ? 'شرایط پرداخت: ' : 'Payment Terms: ';

            if ($rtl_support) {
                $pdf->MultiCell(0, 5, $terms_label . $payment_terms, 0, 'R');
            } else {
                $pdf->MultiCell(0, 5, $terms_label . $payment_terms, 0, 'L');
            }
        }

        // Simple footer
        $pdf->SetY($pdf->getPageHeight() - 30);
        $pdf->SetDrawColor(220, 220, 220);
        $pdf->SetLineWidth(0.3);
        $pdf->Line(25, $pdf->GetY(), $pdf->getPageWidth() - 25, $pdf->GetY());

        $pdf->Ln(5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 2, $rtl_support);
        $pdf->SetTextColor(150, 150, 150);

        $footer_text = $rtl_support ?
            'تولید شده توسط ' . get_option('cfb_company_name', get_bloginfo('name')) :
            'Generated by ' . get_option('cfb_company_name', get_bloginfo('name'));

        $pdf->Cell(0, 4, $footer_text, 0, 1, 'C');
    }

    /**
     * Add clean minimal header
     */
    private function add_clean_minimal_header($pdf, $invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_email = get_option('cfb_company_email', get_option('admin_email'));
        $company_phone = get_option('cfb_company_phone', '');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Logo placement - CORRECTED
        $logo_url = get_option('cfb_company_logo', '');
        if ($logo_url) {
            $logo_path = $this->url_to_path($logo_url);
            if ($logo_path && file_exists($logo_path)) {
                if ($rtl_support) {
                    // RTL: Logo on RIGHT side
                    $pdf->Image($logo_path, $pdf->getPageWidth() - 55, 30, 25, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
                } else {
                    // LTR: Logo on LEFT side
                    $pdf->Image($logo_path, 30, 30, 25, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
                }
            }
        }

        // Company name
        if ($rtl_support) {
            $pdf->SetXY(30, 30);
        } else {
            $pdf->SetXY($logo_url ? 65 : 30, 30);
        }

        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 3, $rtl_support, 'B');
        $pdf->SetTextColor(50, 50, 50);
        $align = $rtl_support ? 'R' : 'L';
        $pdf->Cell(0, 7, $company_name, 0, 1, $align);

        // Contact info
        if ($rtl_support) {
            $pdf->SetX($pdf->getPageWidth() - 40);
        } else {
            $pdf->SetX(30);
        }

        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 2, $rtl_support);
        $pdf->SetTextColor(120, 120, 120);

        $contact_info = array();
        if ($company_email) $contact_info[] = $company_email;
        if ($company_phone) $contact_info[] = $company_phone;

        if (!empty($contact_info)) {
            $pdf->Cell(0, 4, implode(' • ', $contact_info), 0, 1, $align);
        }

        $pdf->Ln(10);
    }

    /**
     * Add clean minimal invoice info
     */
    private function add_clean_minimal_invoice_info($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Invoice title
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 6, $rtl_support, 'B');
        $pdf->SetTextColor(50, 50, 50);

        $custom_title = get_option('cfb_invoice_title', '');
        $invoice_title = !empty($custom_title) ? $custom_title : ($rtl_support ? 'فاکتور' : 'INVOICE');

        if ($rtl_support) {
            $pdf->Cell(0, 10, $invoice_title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 10, $invoice_title, 0, 1, 'L');
        }

        // Invoice details
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(120, 120, 120);

        $invoice_info = $rtl_support ?
            'شماره: ' . $this->convert_to_farsi_digits($invoice->invoice_number) . ' • تاریخ: ' . $this->convert_to_farsi_digits(date('Y/m/d', strtotime($invoice->created_at))) :
            'Invoice #' . $this->convert_to_farsi_digits($invoice->invoice_number) . ' • Date: ' . $this->convert_to_farsi_digits(date('M j, Y', strtotime($invoice->created_at)));

        if ($rtl_support) {
            $pdf->Cell(0, 5, $invoice_info, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 5, $invoice_info, 0, 1, 'L');
        }

        $pdf->Ln(8);
    }

    /**
     * Add clean minimal customer info
     */
    private function add_clean_minimal_customer_info($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Customer label
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support, 'B');
        $pdf->SetTextColor(80, 80, 80);

        $bill_to_label = $rtl_support ? 'صورتحساب برای:' : 'Bill To:';

        if ($rtl_support) {
            $pdf->Cell(0, 5, $bill_to_label, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 5, $bill_to_label, 0, 1, 'L');
        }

        // Customer name
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support, 'B');
        $pdf->SetTextColor(50, 50, 50);

        if ($rtl_support) {
            $pdf->Cell(0, 5, $invoice->customer_name, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 5, $invoice->customer_name, 0, 1, 'L');
        }

        // Customer details
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 2, $rtl_support);
        $pdf->SetTextColor(120, 120, 120);

        $customer_details = array();
        if ($invoice->customer_email) $customer_details[] = $invoice->customer_email;
        if ($invoice->customer_phone) $customer_details[] = $invoice->customer_phone;

        if (!empty($customer_details)) {
            if ($rtl_support) {
                $pdf->Cell(0, 4, implode(' • ', $customer_details), 0, 1, 'R');
            } else {
                $pdf->Cell(0, 4, implode(' • ', $customer_details), 0, 1, 'L');
            }
        }

        $pdf->Ln(8);
    }

    /**
     * Add clean minimal summary
     */
    private function add_clean_minimal_summary($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Summary title
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support, 'B');
        $pdf->SetTextColor(60, 60, 60);

        $summary_title = $rtl_support ? 'خلاصه' : 'Summary';

        if ($rtl_support) {
            $pdf->Cell(0, 6, $summary_title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 6, $summary_title, 0, 1, 'L');
        }

        $pdf->Ln(2);

        // Simple summary table
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(80, 80, 80);
        $pdf->SetDrawColor(220, 220, 220);
        $pdf->SetLineWidth(0.1);

        // Calculation
        $calculation_label = $rtl_support ? 'محاسبه ' . $invoice->form_name : $invoice->form_name . ' Calculation';

        if ($rtl_support) {
            $pdf->Cell(120, 6, $this->format_currency_farsi($invoice->subtotal), 1, 0, 'L');
            $pdf->Cell(50, 6, $calculation_label, 1, 1, 'R');
        } else {
            $pdf->Cell(120, 6, $calculation_label, 1, 0, 'L');
            $pdf->Cell(50, 6, $this->format_currency_farsi($invoice->subtotal), 1, 1, 'R');
        }

        // Tax if applicable
        if ($invoice->tax_amount > 0) {
            $tax_label = $rtl_support ? 'مالیات' : 'Tax';

            if ($rtl_support) {
                $pdf->Cell(120, 6, $this->format_currency_farsi($invoice->tax_amount), 1, 0, 'L');
                $pdf->Cell(50, 6, $tax_label, 1, 1, 'R');
            } else {
                $pdf->Cell(120, 6, $tax_label, 1, 0, 'L');
                $pdf->Cell(50, 6, $this->format_currency_farsi($invoice->tax_amount), 1, 1, 'R');
            }
        }

        // Total
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support, 'B');
        $pdf->SetTextColor(50, 50, 50);
        $pdf->SetFillColor(248, 248, 248);

        $total_label = $rtl_support ? 'جمع کل' : 'Total';

        if ($rtl_support) {
            $pdf->Cell(120, 7, $this->format_currency_farsi($invoice->total_amount), 1, 0, 'L', true);
            $pdf->Cell(50, 7, $total_label, 1, 1, 'R', true);
        } else {
            $pdf->Cell(120, 7, $total_label, 1, 0, 'L', true);
            $pdf->Cell(50, 7, $this->format_currency_farsi($invoice->total_amount), 1, 1, 'R', true);
        }

        $pdf->Ln(8);
    }

    /**
     * Add clean minimal footer
     */
    private function add_clean_minimal_footer($pdf, $invoice) {
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Payment terms
        if ($payment_terms) {
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 2, $rtl_support);
            $pdf->SetTextColor(120, 120, 120);

            $terms_label = $rtl_support ? 'شرایط پرداخت: ' : 'Payment Terms: ';

            if ($rtl_support) {
                $pdf->MultiCell(0, 4, $terms_label . $payment_terms, 0, 'R');
            } else {
                $pdf->MultiCell(0, 4, $terms_label . $payment_terms, 0, 'L');
            }
        }

        // Footer line
        $pdf->SetY($pdf->getPageHeight() - 25);
        $pdf->SetDrawColor(230, 230, 230);
        $pdf->SetLineWidth(0.2);
        $pdf->Line(30, $pdf->GetY(), $pdf->getPageWidth() - 30, $pdf->GetY());

        $pdf->Ln(3);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 3, $rtl_support);
        $pdf->SetTextColor(160, 160, 160);

        $footer_text = $rtl_support ?
            'تولید شده توسط ' . get_option('cfb_company_name', get_bloginfo('name')) :
            'Generated by ' . get_option('cfb_company_name', get_bloginfo('name'));

        $pdf->Cell(0, 3, $footer_text, 0, 1, 'C');
    }

    /**
     * Add minimal header
     */
    private function add_minimal_header($pdf, $invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Simple line separator
        $pdf->SetDrawColor(200, 200, 200);
        $pdf->SetLineWidth(0.1);
        $pdf->Line(15, 25, 195, 25);

        // Company name and logo in one line
        $logo_url = get_option('cfb_company_logo', '');
        if ($logo_url) {
            $logo_path = $this->url_to_path($logo_url);
            if ($logo_path && file_exists($logo_path)) {
                $pdf->Image($logo_path, 165, 15, 25, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
            }
        }

        $pdf->SetXY(15, 15);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support, 'B');
        $pdf->SetTextColor(80, 80, 80);
        $pdf->Cell(0, 8, $company_name, 0, 1, 'L');

        $pdf->SetY(30);
    }

    /**
     * Add minimal details
     */
    private function add_minimal_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Invoice title and details in one line
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 6, $rtl_support, 'B');
        $pdf->SetTextColor(50, 50, 50);

        $custom_title = get_option('cfb_invoice_title', '');
        $invoice_title = !empty($custom_title) ? $custom_title : ($rtl_support ? 'فاکتور' : 'INVOICE');

        $pdf->Cell(100, 10, $invoice_title, 0, 0, 'L');

        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor(120, 120, 120);
        $pdf->Cell(0, 10, '#' . $this->convert_to_farsi_digits($invoice->invoice_number) . ' | ' . $this->convert_to_farsi_digits(date('M j, Y', strtotime($invoice->created_at))), 0, 1, 'R');

        // Customer info in simple format
        $pdf->Ln(5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(100, 100, 100);
        $pdf->Cell(0, 5, 'Bill To: ' . $invoice->customer_name . ' | ' . $invoice->customer_email, 0, 1, 'L');

        $pdf->Ln(5);
    }

    /**
     * Add minimal totals
     */
    private function add_minimal_totals($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));

        // Simple total line
        $pdf->SetDrawColor(200, 200, 200);
        $pdf->SetLineWidth(0.1);
        $pdf->Line(15, $pdf->GetY(), 195, $pdf->GetY());

        $pdf->Ln(5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 4, $rtl_support, 'B');
        $pdf->SetTextColor(50, 50, 50);
        $pdf->Cell(0, 10, 'Total: ' . $this->format_currency_farsi($invoice->total_amount), 0, 1, 'R');

        $pdf->Line(15, $pdf->GetY(), 195, $pdf->GetY());
    }

    /**
     * Add modern professional header
     */
    private function add_modern_professional_header($pdf, $invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));
        $company_website = get_option('cfb_company_website', get_site_url());
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Professional gradient header background
        $pdf->SetFillColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->Rect(0, 0, $pdf->getPageWidth(), 50, 'F');

        // Gradient effect simulation with multiple rectangles
        for ($i = 0; $i < 10; $i++) {
            $alpha = 255 - ($i * 15);
            $pdf->SetAlpha($alpha / 255);
            $pdf->SetFillColor(
                min(255, $colors['primary']['r'] + $i * 5),
                min(255, $colors['primary']['g'] + $i * 5),
                min(255, $colors['primary']['b'] + $i * 5)
            );
            $pdf->Rect(0, $i * 2, $pdf->getPageWidth(), 2, 'F');
        }
        $pdf->SetAlpha(1); // Reset transparency

        // Company logo - positioned in top right with white background circle
        $logo_url = get_option('cfb_company_logo', '');
        if ($logo_url) {
            $logo_path = $this->url_to_path($logo_url);
            if ($logo_path && file_exists($logo_path)) {
                // White circle background for logo
                $pdf->SetFillColor(255, 255, 255);
                $pdf->Circle($pdf->getPageWidth() - 35, 25, 18, 0, 360, 'F');

                // Logo image
                $pdf->Image($logo_path, $pdf->getPageWidth() - 50, 10, 30, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
            }
        }

        // Company name with modern typography
        $pdf->SetXY(15, 15);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 8, $rtl_support, 'B');
        $pdf->SetTextColor(255, 255, 255);
        $pdf->Cell(0, 12, $company_name, 0, 1, 'L');

        // Company tagline or subtitle
        $pdf->SetX(15);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support);
        $pdf->SetTextColor(240, 240, 240);
        $tagline = get_option('cfb_company_tagline', 'Professional Services & Solutions');
        $pdf->Cell(0, 6, $tagline, 0, 1, 'L');

        // Contact info in elegant layout
        $pdf->SetY(35);
        $pdf->SetX(15);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(220, 220, 220);

        $contact_info = array();
        if ($company_phone) $contact_info[] = $company_phone;
        if ($company_email) $contact_info[] = $company_email;
        if ($company_website) $contact_info[] = str_replace(['http://', 'https://'], '', $company_website);

        $pdf->Cell(0, 5, implode(' • ', $contact_info), 0, 1, 'L');

        $pdf->SetY(60);
    }

    /**
     * Add modern invoice details
     */
    private function add_modern_invoice_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Modern card-style invoice details
        $pdf->SetFillColor(248, 250, 252);
        $pdf->SetDrawColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetLineWidth(0.5);
        $pdf->RoundedRect(15, $pdf->GetY(), 180, 25, 3, '1111', 'DF');

        // Invoice title with modern styling
        $pdf->SetXY(20, $pdf->GetY() + 5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 10, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

        $custom_title = get_option('cfb_invoice_title', '');
        $invoice_title = !empty($custom_title) ? $custom_title : ($rtl_support ? 'فاکتور' : 'INVOICE');
        $pdf->Cell(80, 8, $invoice_title, 0, 0, 'L');

        // Invoice number and date in modern layout
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        $invoice_info = '#' . $this->convert_to_farsi_digits($invoice->invoice_number) . ' • ' .
                       $this->convert_to_farsi_digits(date('M j, Y', strtotime($invoice->created_at)));
        $pdf->Cell(0, 8, $invoice_info, 0, 1, 'R');

        $pdf->Ln(15);
    }

    /**
     * Add modern customer details
     */
    private function add_modern_customer_details($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Modern customer card
        $pdf->SetFillColor(255, 255, 255);
        $pdf->SetDrawColor(230, 230, 230);
        $pdf->SetLineWidth(0.3);
        $pdf->RoundedRect(15, $pdf->GetY(), 180, 20, 2, '1111', 'DF');

        // Customer section header
        $pdf->SetXY(20, $pdf->GetY() + 3);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 1, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $bill_to_label = $rtl_support ? 'صورتحساب برای' : 'Bill To';
        $pdf->Cell(0, 5, $bill_to_label, 0, 1, 'L');

        // Customer details in clean format
        $pdf->SetX(20);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support, 'B');
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);
        $pdf->Cell(0, 5, $invoice->customer_name, 0, 1, 'L');

        $pdf->SetX(20);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(120, 120, 120);

        $customer_info = array();
        if ($invoice->customer_email) $customer_info[] = $invoice->customer_email;
        if ($invoice->customer_phone) $customer_info[] = $invoice->customer_phone;

        $pdf->Cell(0, 4, implode(' • ', $customer_info), 0, 1, 'L');

        $pdf->Ln(10);
    }

    /**
     * Add modern invoice items table
     */
    private function add_modern_invoice_items_table($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Modern table with gradient header
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support, 'B');

        // Gradient header background
        $pdf->SetFillColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetDrawColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetLineWidth(0.3);

        // Table headers with modern styling
        if ($rtl_support) {
            $headers = array(
                'جمع کل' => 40,
                'قیمت واحد' => 35,
                'تعداد' => 25,
                'شرح' => 80
            );
            $aligns = array('R', 'R', 'C', 'R');
        } else {
            $headers = array(
                'Description' => 80,
                'Quantity' => 25,
                'Unit Price' => 35,
                'Total' => 40
            );
            $aligns = array('L', 'C', 'R', 'R');
        }

        $i = 0;
        foreach ($headers as $header => $width) {
            $pdf->Cell($width, 10, $header, 1, 0, $aligns[$i], true);
            $i++;
        }
        $pdf->Ln();

        // Table content with alternating row colors
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        $row_count = 0;
        if (!empty($invoice->items)) {
            foreach ($invoice->items as $item) {
                // Alternating row colors
                if ($row_count % 2 == 0) {
                    $pdf->SetFillColor(248, 250, 252);
                } else {
                    $pdf->SetFillColor(255, 255, 255);
                }

                if ($rtl_support) {
                    $pdf->Cell(40, 8, $this->format_currency_farsi($item->total_price), 1, 0, 'R', true);
                    $pdf->Cell(35, 8, $this->format_currency_farsi($item->unit_price), 1, 0, 'R', true);
                    $pdf->Cell(25, 8, $this->convert_to_farsi_digits(number_format($item->quantity, 2)), 1, 0, 'C', true);
                    $pdf->Cell(80, 8, $item->item_name, 1, 1, 'R', true);
                } else {
                    $pdf->Cell(80, 8, $item->item_name, 1, 0, 'L', true);
                    $pdf->Cell(25, 8, $this->convert_to_farsi_digits(number_format($item->quantity, 2)), 1, 0, 'C', true);
                    $pdf->Cell(35, 8, $this->format_currency_farsi($item->unit_price), 1, 0, 'R', true);
                    $pdf->Cell(40, 8, $this->format_currency_farsi($item->total_price), 1, 1, 'R', true);
                }
                $row_count++;
            }
        } else {
            // Single calculation item
            $calculation_label = $rtl_support ? 'محاسبه ' . $invoice->form_name : $invoice->form_name . ' Calculation';

            $pdf->SetFillColor(248, 250, 252);
            if ($rtl_support) {
                $pdf->Cell(40, 8, $this->format_currency_farsi($invoice->subtotal), 1, 0, 'R', true);
                $pdf->Cell(35, 8, $this->format_currency_farsi($invoice->subtotal), 1, 0, 'R', true);
                $pdf->Cell(25, 8, $this->convert_to_farsi_digits('1'), 1, 0, 'C', true);
                $pdf->Cell(80, 8, $calculation_label, 1, 1, 'R', true);
            } else {
                $pdf->Cell(80, 8, $calculation_label, 1, 0, 'L', true);
                $pdf->Cell(25, 8, $this->convert_to_farsi_digits('1'), 1, 0, 'C', true);
                $pdf->Cell(35, 8, $this->format_currency_farsi($invoice->subtotal), 1, 0, 'R', true);
                $pdf->Cell(40, 8, $this->format_currency_farsi($invoice->subtotal), 1, 1, 'R', true);
            }
        }

        $pdf->Ln(8);
    }

    /**
     * Add modern invoice totals
     */
    private function add_modern_invoice_totals($pdf, $invoice) {
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Modern totals card
        $pdf->SetFillColor(248, 250, 252);
        $pdf->SetDrawColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->SetLineWidth(0.3);
        $pdf->RoundedRect(120, $pdf->GetY(), 75, 25, 2, '1111', 'DF');

        $start_y = $pdf->GetY();

        // Subtotal
        $pdf->SetXY(125, $start_y + 3);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support);
        $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);

        $subtotal_label = $rtl_support ? 'جمع جزء:' : 'Subtotal:';
        $pdf->Cell(35, 5, $subtotal_label, 0, 0, 'L');
        $pdf->Cell(30, 5, $this->format_currency_farsi($invoice->subtotal), 0, 1, 'R');

        // Tax (if applicable)
        if ($invoice->tax_amount > 0) {
            $pdf->SetX(125);
            $tax_label = $rtl_support ? 'مالیات:' : 'Tax:';
            $pdf->Cell(35, 5, $tax_label, 0, 0, 'L');
            $pdf->Cell(30, 5, $this->format_currency_farsi($invoice->tax_amount), 0, 1, 'R');
        }

        // Total with emphasis
        $pdf->SetX(125);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size + 2, $rtl_support, 'B');
        $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

        $total_label = $rtl_support ? 'جمع کل:' : 'Total:';
        $pdf->Cell(35, 6, $total_label, 0, 0, 'L');
        $pdf->Cell(30, 6, $this->format_currency_farsi($invoice->total_amount), 0, 1, 'R');

        $pdf->Ln(15);
    }

    /**
     * Add modern professional footer
     */
    private function add_modern_professional_footer($pdf, $invoice) {
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
        $rtl_support = get_option('cfb_pdf_rtl_support', 0);
        $font_size = intval(get_option('cfb_pdf_font_size', 10));
        $color_scheme = get_option('cfb_pdf_color_scheme', 'blue');
        $colors = $this->get_color_scheme_colors($color_scheme);

        // Payment terms in modern card
        if ($payment_terms) {
            $pdf->SetFillColor(255, 255, 255);
            $pdf->SetDrawColor(230, 230, 230);
            $pdf->SetLineWidth(0.3);
            $pdf->RoundedRect(15, $pdf->GetY(), 180, 15, 2, '1111', 'DF');

            $pdf->SetXY(20, $pdf->GetY() + 3);
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size, $rtl_support, 'B');
            $pdf->SetTextColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);

            $payment_terms_label = $rtl_support ? 'شرایط پرداخت:' : 'Payment Terms:';
            $pdf->Cell(0, 5, $payment_terms_label, 0, 1, 'L');

            $pdf->SetX(20);
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
            $pdf->SetTextColor($colors['accent']['r'], $colors['accent']['g'], $colors['accent']['b']);
            $pdf->MultiCell(160, 4, $payment_terms, 0, 'L');
        }

        // Professional footer with gradient
        $pdf->SetY($pdf->getPageHeight() - 30);
        $pdf->SetFillColor($colors['primary']['r'], $colors['primary']['g'], $colors['primary']['b']);
        $pdf->Rect(0, $pdf->GetY(), $pdf->getPageWidth(), 30, 'F');

        $pdf->SetXY(15, $pdf->GetY() + 8);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'helvetica'), $font_size - 1, $rtl_support);
        $pdf->SetTextColor(255, 255, 255);

        $footer_text = $rtl_support ?
            'این فاکتور توسط ' . get_option('cfb_company_name', get_bloginfo('name')) . ' تولید شده است.' :
            'Generated by ' . get_option('cfb_company_name', get_bloginfo('name'));

        $pdf->Cell(0, 5, $footer_text, 0, 1, 'C');

        $pdf->SetX(15);
        $generation_date = $rtl_support ?
            'تاریخ تولید: ' . $this->convert_to_farsi_digits(date('Y/m/d H:i')) :
            'Generated on: ' . date('F j, Y \a\t g:i A');

        $pdf->Cell(0, 4, $generation_date, 0, 1, 'C');
    }

    /**
     * Generate simple PDF fallback (when TCPDF is not available)
     */
    private function generate_simple_pdf($invoice) {
        // Create a simple HTML-based PDF using WordPress functionality
        $html = $this->generate_invoice_html($invoice);

        // Save as HTML file for now (in production, you'd use a PDF library)
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';

        if (!file_exists($pdf_dir)) {
            wp_mkdir_p($pdf_dir);
        }

        $filename = 'invoice-' . $invoice->invoice_number . '.html';
        $filepath = $pdf_dir . $filename;

        file_put_contents($filepath, $html);

        return 'cfb-invoices/' . $filename;
    }

    /**
     * Generate invoice HTML
     */
    private function generate_invoice_html($invoice) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $company_address = get_option('cfb_company_address', '');
        $company_phone = get_option('cfb_company_phone', '');
        $company_email = get_option('cfb_company_email', get_option('admin_email'));

        ob_start();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Invoice <?php echo esc_html($invoice->invoice_number); ?></title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
                .invoice-header { display: flex; justify-content: space-between; margin-bottom: 30px; }
                .company-info { text-align: right; }
                .invoice-title { font-size: 24px; font-weight: bold; margin-bottom: 20px; }
                .invoice-details { margin-bottom: 20px; }
                .customer-info { margin-bottom: 30px; }
                .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                .items-table th { background-color: #f5f5f5; font-weight: bold; }
                .totals { text-align: right; margin-bottom: 30px; }
                .total-line { margin: 5px 0; }
                .total-amount { font-size: 18px; font-weight: bold; }
                .notes { margin-top: 30px; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="invoice-header">
                <div class="company-info">
                    <h2><?php echo esc_html($company_name); ?></h2>
                    <?php if ($company_address): ?>
                        <p><?php echo nl2br(esc_html($company_address)); ?></p>
                    <?php endif; ?>
                    <?php if ($company_phone): ?>
                        <p>Phone: <?php echo esc_html($company_phone); ?></p>
                    <?php endif; ?>
                    <?php if ($company_email): ?>
                        <p>Email: <?php echo esc_html($company_email); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <div class="invoice-title">INVOICE</div>

            <div class="invoice-details">
                <p><strong>Invoice Number:</strong> <?php echo esc_html($invoice->invoice_number); ?></p>
                <p><strong>Invoice Date:</strong> <?php echo date('F j, Y', strtotime($invoice->created_at)); ?></p>
            </div>

            <div class="customer-info">
                <h3>Bill To:</h3>
                <p><strong><?php echo esc_html($invoice->customer_name); ?></strong></p>
                <?php if ($invoice->customer_email): ?>
                    <p><?php echo esc_html($invoice->customer_email); ?></p>
                <?php endif; ?>
                <?php if ($invoice->customer_phone): ?>
                    <p><?php echo esc_html($invoice->customer_phone); ?></p>
                <?php endif; ?>
                <?php if ($invoice->customer_address): ?>
                    <p><?php echo nl2br(esc_html($invoice->customer_address)); ?></p>
                <?php endif; ?>
            </div>

            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($invoice->items)): ?>
                        <?php foreach ($invoice->items as $item): ?>
                            <tr>
                                <td><?php echo esc_html($item->item_name); ?></td>
                                <td><?php echo number_format($item->quantity, 2); ?></td>
                                <td><?php echo $this->format_currency($item->unit_price); ?></td>
                                <td><?php echo $this->format_currency($item->total_price); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td><?php echo esc_html($invoice->form_name . ' Calculation'); ?></td>
                            <td>1</td>
                            <td><?php echo $this->format_currency($invoice->subtotal); ?></td>
                            <td><?php echo $this->format_currency($invoice->subtotal); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>

            <div class="totals">
                <div class="total-line">Subtotal: <?php echo $this->format_currency($invoice->subtotal); ?></div>
                <?php if ($invoice->tax_amount > 0): ?>
                    <div class="total-line">Tax: <?php echo $this->format_currency($invoice->tax_amount); ?></div>
                <?php endif; ?>
                <div class="total-line total-amount">Total: <?php echo $this->format_currency($invoice->total_amount); ?></div>
            </div>

            <?php if ($invoice->notes): ?>
                <div class="notes">
                    <h3>Notes:</h3>
                    <p><?php echo nl2br(esc_html($invoice->notes)); ?></p>
                </div>
            <?php endif; ?>

            <?php
            $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');
            if ($payment_terms):
            ?>
                <div class="notes">
                    <h3>Payment Terms:</h3>
                    <p><?php echo nl2br(esc_html($payment_terms)); ?></p>
                </div>
            <?php endif; ?>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }

    /**
     * Get PDF URL
     */
    private function get_pdf_url($pdf_path) {
        $upload_dir = wp_upload_dir();
        return $upload_dir['baseurl'] . '/' . $pdf_path;
    }

    /**
     * Format currency for display
     */
    private function format_currency($amount) {
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $currency_position = get_option('cfb_currency_position', 'left');
        $decimal_places = get_option('cfb_decimal_places', 2);

        $formatted = number_format($amount, $decimal_places);

        if ($currency_position === 'right') {
            return $formatted . ' ' . $currency_symbol;
        } else {
            return $currency_symbol . ' ' . $formatted;
        }
    }

    /**
     * SIMPLE HEADER - Clean and Working
     */
    private function add_simple_header($pdf, $invoice, $rtl_support) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $logo_url = get_option('cfb_company_logo', '');

        // Add logo if available
        if ($logo_url) {
            $logo_path = $this->url_to_path($logo_url);
            if ($logo_path && file_exists($logo_path)) {
                if ($rtl_support) {
                    // RTL: Logo on RIGHT side
                    $pdf->Image($logo_path, 160, 20, 30, 0);
                } else {
                    // LTR: Logo on LEFT side
                    $pdf->Image($logo_path, 20, 20, 30, 0);
                }
            }
        }

        // Company name
        $pdf->SetFont('helvetica', 'B', 16);
        $pdf->SetTextColor(50, 50, 50);

        if ($rtl_support) {
            $pdf->SetXY(20, 25);
            $pdf->Cell(120, 10, $company_name, 0, 1, 'R');
        } else {
            $pdf->SetXY($logo_url ? 60 : 20, 25);
            $pdf->Cell(120, 10, $company_name, 0, 1, 'L');
        }

        $pdf->Ln(15);
    }

    /**
     * SIMPLE INVOICE INFO - Clear and Direct
     */
    private function add_simple_invoice_info($pdf, $invoice, $rtl_support) {
        // Invoice title
        $pdf->SetFont('helvetica', 'B', 20);
        $pdf->SetTextColor(40, 40, 40);

        $title = $rtl_support ? 'فاکتور' : 'INVOICE';

        if ($rtl_support) {
            $pdf->Cell(0, 12, $title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 12, $title, 0, 1, 'L');
        }

        // Invoice details
        $pdf->SetFont('helvetica', '', 10);
        $pdf->SetTextColor(100, 100, 100);

        $invoice_info = $rtl_support ?
            'شماره: ' . $invoice->invoice_number . ' | تاریخ: ' . date('Y/m/d', strtotime($invoice->created_at)) :
            'Invoice #' . $invoice->invoice_number . ' | Date: ' . date('M j, Y', strtotime($invoice->created_at));

        if ($rtl_support) {
            $pdf->Cell(0, 6, $invoice_info, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 6, $invoice_info, 0, 1, 'L');
        }

        $pdf->Ln(10);
    }

    /**
     * SIMPLE CUSTOMER INFO - Clean Display
     */
    private function add_simple_customer_info($pdf, $invoice, $rtl_support) {
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->SetTextColor(60, 60, 60);

        $bill_to = $rtl_support ? 'صورتحساب برای:' : 'Bill To:';

        if ($rtl_support) {
            $pdf->Cell(0, 8, $bill_to, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 8, $bill_to, 0, 1, 'L');
        }

        // Customer name
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->SetTextColor(40, 40, 40);

        if ($rtl_support) {
            $pdf->Cell(0, 6, $invoice->customer_name, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 6, $invoice->customer_name, 0, 1, 'L');
        }

        // Customer contact
        $pdf->SetFont('helvetica', '', 9);
        $pdf->SetTextColor(120, 120, 120);

        $contact = $invoice->customer_email;
        if ($invoice->customer_phone) {
            $contact .= ' | ' . $invoice->customer_phone;
        }

        if ($rtl_support) {
            $pdf->Cell(0, 5, $contact, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 5, $contact, 0, 1, 'L');
        }

        $pdf->Ln(10);
    }

    /**
     * SIMPLE FORM FIELDS - FIXED TO SHOW REAL DATA
     */
    private function add_simple_form_fields($pdf, $invoice, $rtl_support) {
        // Get form data - try multiple sources
        $form_data = array();

        // First try: invoice form_data column
        if (isset($invoice->form_data) && !empty($invoice->form_data)) {
            $decoded = json_decode($invoice->form_data, true);
            if (is_array($decoded)) {
                $form_data = $decoded;
            }
        }

        // Second try: submission data if available
        if (empty($form_data) && isset($invoice->submission_id) && $invoice->submission_id) {
            global $wpdb;
            $submission = $wpdb->get_row($wpdb->prepare("
                SELECT submission_data FROM {$wpdb->prefix}cfb_form_submissions
                WHERE id = %d
            ", $invoice->submission_id));

            if ($submission && $submission->submission_data) {
                $decoded = json_decode($submission->submission_data, true);
                if (is_array($decoded)) {
                    $form_data = $decoded;
                }
            }
        }

        // Log for debugging
        error_log('CFB PDF: Form data retrieved: ' . print_r($form_data, true));

        if (empty($form_data)) {
            // Show a message instead of fake data
            $pdf->SetFont('helvetica', 'I', 9);
            $pdf->SetTextColor(120, 120, 120);
            $no_data_msg = $rtl_support ? 'هیچ داده فرمی موجود نیست' : 'No form data available';
            $pdf->Cell(0, 6, $no_data_msg, 0, 1, $rtl_support ? 'R' : 'L');
            $pdf->Ln(5);
            return;
        }

        // Form fields section title
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->SetTextColor(60, 60, 60);

        $form_title = $rtl_support ? 'جزئیات پروژه' : 'Project Details';

        if ($rtl_support) {
            $pdf->Cell(0, 8, $form_title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 8, $form_title, 0, 1, 'L');
        }

        $pdf->Ln(3);

        // Simple table for form fields
        $pdf->SetFont('helvetica', '', 9);
        $pdf->SetTextColor(80, 80, 80);
        $pdf->SetDrawColor(200, 200, 200);
        $pdf->SetLineWidth(0.2);

        foreach ($form_data as $field_name => $field_value) {
            if (is_array($field_value)) {
                $field_value = implode(', ', $field_value);
            }

            // Clean field name
            $clean_field_name = ucwords(str_replace(['_', '-'], ' ', $field_name));

            if ($rtl_support) {
                $pdf->Cell(90, 6, $field_value, 1, 0, 'R');
                $pdf->Cell(90, 6, $clean_field_name, 1, 1, 'R');
            } else {
                $pdf->Cell(90, 6, $clean_field_name, 1, 0, 'L');
                $pdf->Cell(90, 6, $field_value, 1, 1, 'L');
            }
        }

        $pdf->Ln(10);
    }

    /**
     * SIMPLE TOTALS - Fixed Currency Display
     */
    private function add_simple_totals($pdf, $invoice, $rtl_support) {
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->SetTextColor(60, 60, 60);

        $summary_title = $rtl_support ? 'خلاصه محاسبه' : 'Calculation Summary';

        if ($rtl_support) {
            $pdf->Cell(0, 8, $summary_title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 8, $summary_title, 0, 1, 'L');
        }

        $pdf->Ln(3);

        // Simple currency formatting - NO REVERSAL
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $decimal_places = intval(get_option('cfb_decimal_places', 2));

        // Format amounts properly
        $subtotal_formatted = number_format($invoice->subtotal, $decimal_places);
        $tax_formatted = number_format($invoice->tax_amount, $decimal_places);
        $total_formatted = number_format($invoice->total_amount, $decimal_places);

        // For RTL, convert to Farsi digits but keep correct order
        if ($rtl_support) {
            $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            $farsi_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];

            $subtotal_formatted = str_replace($english_digits, $farsi_digits, $subtotal_formatted);
            $tax_formatted = str_replace($english_digits, $farsi_digits, $tax_formatted);
            $total_formatted = str_replace($english_digits, $farsi_digits, $total_formatted);
        }

        // Add currency symbol
        $subtotal_display = $rtl_support ? $subtotal_formatted . ' ' . $currency_symbol : $currency_symbol . ' ' . $subtotal_formatted;
        $tax_display = $rtl_support ? $tax_formatted . ' ' . $currency_symbol : $currency_symbol . ' ' . $tax_formatted;
        $total_display = $rtl_support ? $total_formatted . ' ' . $currency_symbol : $currency_symbol . ' ' . $total_formatted;

        // Display totals table
        $pdf->SetFont('helvetica', '', 10);
        $pdf->SetTextColor(80, 80, 80);
        $pdf->SetDrawColor(200, 200, 200);

        // Subtotal
        $subtotal_label = $rtl_support ? 'محاسبه' : 'Calculation';
        if ($rtl_support) {
            $pdf->Cell(90, 7, $subtotal_display, 1, 0, 'L');
            $pdf->Cell(90, 7, $subtotal_label, 1, 1, 'R');
        } else {
            $pdf->Cell(90, 7, $subtotal_label, 1, 0, 'L');
            $pdf->Cell(90, 7, $subtotal_display, 1, 1, 'R');
        }

        // Tax if applicable
        if ($invoice->tax_amount > 0) {
            $tax_label = $rtl_support ? 'مالیات' : 'Tax';
            if ($rtl_support) {
                $pdf->Cell(90, 7, $tax_display, 1, 0, 'L');
                $pdf->Cell(90, 7, $tax_label, 1, 1, 'R');
            } else {
                $pdf->Cell(90, 7, $tax_label, 1, 0, 'L');
                $pdf->Cell(90, 7, $tax_display, 1, 1, 'R');
            }
        }

        // Total
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->SetFillColor(245, 245, 245);

        $total_label = $rtl_support ? 'جمع کل' : 'Total';
        if ($rtl_support) {
            $pdf->Cell(90, 8, $total_display, 1, 0, 'L', true);
            $pdf->Cell(90, 8, $total_label, 1, 1, 'R', true);
        } else {
            $pdf->Cell(90, 8, $total_label, 1, 0, 'L', true);
            $pdf->Cell(90, 8, $total_display, 1, 1, 'R', true);
        }

        $pdf->Ln(10);
    }

    /**
     * SIMPLE FOOTER - Clean and Simple
     */
    private function add_simple_footer($pdf, $invoice, $rtl_support) {
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');

        if ($payment_terms) {
            $pdf->SetFont('helvetica', '', 9);
            $pdf->SetTextColor(120, 120, 120);

            $terms_label = $rtl_support ? 'شرایط پرداخت: ' : 'Payment Terms: ';

            if ($rtl_support) {
                $pdf->MultiCell(0, 5, $terms_label . $payment_terms, 0, 'R');
            } else {
                $pdf->MultiCell(0, 5, $terms_label . $payment_terms, 0, 'L');
            }
        }

        // Simple footer line
        $pdf->SetY($pdf->getPageHeight() - 25);
        $pdf->SetDrawColor(220, 220, 220);
        $pdf->Line(20, $pdf->GetY(), $pdf->getPageWidth() - 20, $pdf->GetY());

        $pdf->Ln(5);
        $pdf->SetFont('helvetica', '', 8);
        $pdf->SetTextColor(150, 150, 150);

        $footer_text = $rtl_support ?
            'تولید شده توسط ' . get_option('cfb_company_name', get_bloginfo('name')) :
            'Generated by ' . get_option('cfb_company_name', get_bloginfo('name'));

        $pdf->Cell(0, 4, $footer_text, 0, 1, 'C');
    }

    // ==========================================
    // MODERN TEMPLATE METHODS - PROFESSIONAL COMMERCIAL DESIGN
    // ==========================================

    /**
     * MODERN HEADER - Professional with gradient-style design
     */
    private function add_modern_header($pdf, $invoice, $rtl_support) {
        $company_name = get_option('cfb_company_name', get_bloginfo('name'));
        $logo_url = get_option('cfb_company_logo', '');

        // Modern gradient-style header background
        $pdf->SetFillColor(67, 126, 235); // Modern blue gradient
        $pdf->Rect(15, 15, 180, 25, 'F');

        // Add logo if available
        if ($logo_url) {
            $logo_path = $this->url_to_path($logo_url);
            if ($logo_path && file_exists($logo_path)) {
                if ($rtl_support) {
                    // RTL: Logo on RIGHT side
                    $pdf->Image($logo_path, 160, 18, 30, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
                } else {
                    // LTR: Logo on LEFT side
                    $pdf->Image($logo_path, 20, 18, 30, 0, '', '', 'T', false, 300, '', false, false, 0, false, false, false);
                }
            }
        }

        // Company name in white on gradient
        $pdf->SetTextColor(255, 255, 255);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 16, $rtl_support, 'B');

        if ($rtl_support) {
            $pdf->SetXY(20, 25);
            $pdf->Cell(120, 8, $company_name, 0, 1, 'R');
        } else {
            $pdf->SetXY(60, 25);
            $pdf->Cell(120, 8, $company_name, 0, 1, 'L');
        }

        $pdf->SetY(50);
    }

    /**
     * MODERN INVOICE INFO - Clean professional layout
     */
    private function add_modern_invoice_info($pdf, $invoice, $rtl_support) {
        // Invoice title with modern styling
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 24, $rtl_support, 'B');
        $pdf->SetTextColor(67, 126, 235);

        $invoice_title = get_option('cfb_invoice_title', '');
        if (empty($invoice_title)) {
            $invoice_title = $rtl_support ? 'فاکتور' : 'INVOICE';
        }

        if ($rtl_support) {
            $pdf->Cell(0, 12, $invoice_title, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 12, $invoice_title, 0, 1, 'L');
        }

        // Invoice details in modern card-style
        $pdf->SetFillColor(248, 249, 250);
        $pdf->Rect(15, $pdf->GetY() + 5, 180, 20, 'F');

        $pdf->SetY($pdf->GetY() + 8);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 10, $rtl_support);
        $pdf->SetTextColor(60, 60, 60);

        $invoice_info = ($rtl_support ? 'شماره فاکتور: ' : 'Invoice #') . $invoice->invoice_number .
                       ' | ' . ($rtl_support ? 'تاریخ: ' : 'Date: ') . date('Y-m-d', strtotime($invoice->created_at));

        if ($rtl_support) {
            $pdf->Cell(0, 6, $invoice_info, 0, 1, 'R');
        } else {
            $pdf->Cell(0, 6, $invoice_info, 0, 1, 'L');
        }

        $pdf->Ln(15);
    }

    /**
     * MODERN CUSTOMER INFO - Professional card design
     */
    private function add_modern_customer_info($pdf, $invoice, $rtl_support) {
        // Customer info card with border
        $pdf->SetDrawColor(67, 126, 235);
        $pdf->SetLineWidth(0.5);
        $pdf->Rect(15, $pdf->GetY(), 180, 35, 'D');

        $pdf->SetY($pdf->GetY() + 5);
        $pdf->SetX(20);

        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 12, $rtl_support, 'B');
        $pdf->SetTextColor(67, 126, 235);

        $bill_to = $rtl_support ? 'صورتحساب برای:' : 'Bill To:';
        $pdf->Cell(0, 8, $bill_to, 0, 1, $rtl_support ? 'R' : 'L');

        // Customer details
        $pdf->SetX(20);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 11, $rtl_support, 'B');
        $pdf->SetTextColor(40, 40, 40);
        $pdf->Cell(0, 6, $invoice->customer_name, 0, 1, $rtl_support ? 'R' : 'L');

        $pdf->SetX(20);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 9, $rtl_support);
        $pdf->SetTextColor(100, 100, 100);

        $contact = $invoice->customer_email;
        if ($invoice->customer_phone) {
            $contact .= ' | ' . $invoice->customer_phone;
        }
        $pdf->Cell(0, 5, $contact, 0, 1, $rtl_support ? 'R' : 'L');

        if ($invoice->customer_address) {
            $pdf->SetX(20);
            $pdf->Cell(0, 5, $invoice->customer_address, 0, 1, $rtl_support ? 'R' : 'L');
        }

        $pdf->Ln(15);
    }

    /**
     * MODERN FORM FIELDS - Professional table with modern styling
     */
    private function add_modern_form_fields($pdf, $invoice, $rtl_support) {
        // Get form data using the same logic as simple method
        $form_data = array();

        if (isset($invoice->form_data) && !empty($invoice->form_data)) {
            $decoded = json_decode($invoice->form_data, true);
            if (is_array($decoded)) {
                $form_data = $decoded;
            }
        }

        if (empty($form_data) && isset($invoice->submission_id) && $invoice->submission_id) {
            global $wpdb;
            $submission = $wpdb->get_row($wpdb->prepare("
                SELECT submission_data FROM {$wpdb->prefix}cfb_form_submissions
                WHERE id = %d
            ", $invoice->submission_id));

            if ($submission && $submission->submission_data) {
                $decoded = json_decode($submission->submission_data, true);
                if (is_array($decoded)) {
                    $form_data = $decoded;
                }
            }
        }

        if (empty($form_data)) {
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 9, $rtl_support, 'I');
            $pdf->SetTextColor(120, 120, 120);
            $no_data_msg = $rtl_support ? 'هیچ داده فرمی موجود نیست' : 'No form data available';
            $pdf->Cell(0, 6, $no_data_msg, 0, 1, $rtl_support ? 'R' : 'L');
            $pdf->Ln(5);
            return;
        }

        // Section title
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 14, $rtl_support, 'B');
        $pdf->SetTextColor(67, 126, 235);
        $form_title = $rtl_support ? 'جزئیات پروژه' : 'Project Details';
        $pdf->Cell(0, 8, $form_title, 0, 1, $rtl_support ? 'R' : 'L');

        $pdf->Ln(3);

        // Modern table with alternating colors
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 9, $rtl_support);
        $pdf->SetDrawColor(220, 220, 220);
        $pdf->SetLineWidth(0.1);

        $row_count = 0;
        foreach ($form_data as $field_name => $field_value) {
            if (is_array($field_value)) {
                $field_value = implode(', ', $field_value);
            }

            // Alternating row colors
            if ($row_count % 2 == 0) {
                $pdf->SetFillColor(248, 249, 250);
            } else {
                $pdf->SetFillColor(255, 255, 255);
            }

            $clean_field_name = ucwords(str_replace(['_', '-'], ' ', $field_name));

            if ($rtl_support) {
                $pdf->Cell(90, 8, $field_value, 1, 0, 'R', true);
                $pdf->Cell(90, 8, $clean_field_name, 1, 1, 'R', true);
            } else {
                $pdf->Cell(90, 8, $clean_field_name, 1, 0, 'L', true);
                $pdf->Cell(90, 8, $field_value, 1, 1, 'L', true);
            }

            $row_count++;
        }

        $pdf->Ln(10);
    }

    /**
     * MODERN TOTALS - Professional calculation summary
     */
    private function add_modern_totals($pdf, $invoice, $rtl_support) {
        // Section title
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 14, $rtl_support, 'B');
        $pdf->SetTextColor(67, 126, 235);
        $summary_title = $rtl_support ? 'خلاصه محاسبه' : 'Calculation Summary';
        $pdf->Cell(0, 8, $summary_title, 0, 1, $rtl_support ? 'R' : 'L');

        $pdf->Ln(3);

        // Modern totals card
        $pdf->SetFillColor(248, 249, 250);
        $pdf->SetDrawColor(67, 126, 235);
        $pdf->SetLineWidth(0.5);
        $pdf->Rect(15, $pdf->GetY(), 180, 30, 'DF');

        $pdf->SetY($pdf->GetY() + 5);

        // Currency formatting
        $currency_symbol = get_option('cfb_currency_symbol', '$');
        $decimal_places = intval(get_option('cfb_decimal_places', 2));

        $subtotal_formatted = number_format($invoice->subtotal, $decimal_places);
        $tax_formatted = number_format($invoice->tax_amount, $decimal_places);
        $total_formatted = number_format($invoice->total_amount, $decimal_places);

        // Convert to Farsi digits if RTL
        if ($rtl_support) {
            $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            $farsi_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];

            $subtotal_formatted = str_replace($english_digits, $farsi_digits, $subtotal_formatted);
            $tax_formatted = str_replace($english_digits, $farsi_digits, $tax_formatted);
            $total_formatted = str_replace($english_digits, $farsi_digits, $total_formatted);
        }

        // Format with currency
        $subtotal_display = $rtl_support ? $subtotal_formatted . ' ' . $currency_symbol : $currency_symbol . ' ' . $subtotal_formatted;
        $tax_display = $rtl_support ? $tax_formatted . ' ' . $currency_symbol : $currency_symbol . ' ' . $tax_formatted;
        $total_display = $rtl_support ? $total_formatted . ' ' . $currency_symbol : $currency_symbol . ' ' . $total_formatted;

        // Display totals
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 10, $rtl_support);
        $pdf->SetTextColor(60, 60, 60);

        // Subtotal
        $subtotal_label = $rtl_support ? 'محاسبه:' : 'Calculation:';
        if ($rtl_support) {
            $pdf->SetX(25);
            $pdf->Cell(80, 6, $subtotal_label, 0, 0, 'R');
            $pdf->Cell(80, 6, $subtotal_display, 0, 1, 'L');
        } else {
            $pdf->SetX(25);
            $pdf->Cell(80, 6, $subtotal_label, 0, 0, 'L');
            $pdf->Cell(80, 6, $subtotal_display, 0, 1, 'R');
        }

        // Tax if applicable
        if ($invoice->tax_amount > 0) {
            $tax_label = $rtl_support ? 'مالیات:' : 'Tax:';
            if ($rtl_support) {
                $pdf->SetX(25);
                $pdf->Cell(80, 6, $tax_label, 0, 0, 'R');
                $pdf->Cell(80, 6, $tax_display, 0, 1, 'L');
            } else {
                $pdf->SetX(25);
                $pdf->Cell(80, 6, $tax_label, 0, 0, 'L');
                $pdf->Cell(80, 6, $tax_display, 0, 1, 'R');
            }
        }

        // Total with emphasis
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 12, $rtl_support, 'B');
        $pdf->SetTextColor(67, 126, 235);

        $total_label = $rtl_support ? 'جمع کل:' : 'Total:';
        if ($rtl_support) {
            $pdf->SetX(25);
            $pdf->Cell(80, 8, $total_label, 0, 0, 'R');
            $pdf->Cell(80, 8, $total_display, 0, 1, 'L');
        } else {
            $pdf->SetX(25);
            $pdf->Cell(80, 8, $total_label, 0, 0, 'L');
            $pdf->Cell(80, 8, $total_display, 0, 1, 'R');
        }

        $pdf->Ln(15);
    }

    /**
     * MODERN FOOTER - Clean professional footer
     */
    private function add_modern_footer($pdf, $invoice, $rtl_support) {
        $payment_terms = get_option('cfb_payment_terms', 'Payment is due within 30 days.');

        if ($payment_terms) {
            $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 9, $rtl_support);
            $pdf->SetTextColor(100, 100, 100);

            $terms_label = $rtl_support ? 'شرایط پرداخت: ' : 'Payment Terms: ';
            $pdf->MultiCell(0, 5, $terms_label . $payment_terms, 0, $rtl_support ? 'R' : 'L');
        }

        // Modern footer with gradient
        $pdf->SetY($pdf->getPageHeight() - 30);
        $pdf->SetFillColor(67, 126, 235);
        $pdf->Rect(15, $pdf->GetY(), 180, 15, 'F');

        $pdf->SetY($pdf->GetY() + 5);
        $this->set_pdf_font($pdf, get_option('cfb_pdf_font_family', 'dejavusans'), 8, $rtl_support);
        $pdf->SetTextColor(255, 255, 255);

        $footer_text = $rtl_support ?
            'تولید شده توسط ' . get_option('cfb_company_name', get_bloginfo('name')) :
            'Generated by ' . get_option('cfb_company_name', get_bloginfo('name'));

        $pdf->Cell(0, 5, $footer_text, 0, 1, 'C');
    }




}
