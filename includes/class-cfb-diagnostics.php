<?php
/**
 * CFB Calculator Diagnostics
 * Handles system diagnostics and testing
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Diagnostics {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_cfb_run_database_check', array($this, 'ajax_run_database_check'));
        add_action('wp_ajax_cfb_test_pdf_generation', array($this, 'ajax_test_pdf_generation'));
        add_action('wp_ajax_cfb_run_form_data_test', array($this, 'ajax_run_form_data_test'));
    }
    
    /**
     * Render diagnostics page
     */
    public function render_diagnostics_page() {
        ?>
        <div class="wrap cfb-diagnostics">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-admin-tools"></span>
                <?php _e('CFB Calculator - System Diagnostics', 'cfb-calculator'); ?>
            </h1>
            
            <div class="cfb-diagnostics-container">
                <!-- Database Status -->
                <div class="cfb-diagnostic-section">
                    <h2><?php _e('Database Status', 'cfb-calculator'); ?></h2>
                    <div class="cfb-diagnostic-content">
                        <p><?php _e('Check database tables and structure for any issues.', 'cfb-calculator'); ?></p>
                        <button type="button" class="button button-primary" id="cfb-check-database">
                            <?php _e('Check Database', 'cfb-calculator'); ?>
                        </button>
                        <div id="cfb-database-results" class="cfb-results-container" style="display: none;"></div>
                    </div>
                </div>

                <!-- PDF Generation Test -->
                <div class="cfb-diagnostic-section">
                    <h2><?php _e('PDF Generation Test', 'cfb-calculator'); ?></h2>
                    <div class="cfb-diagnostic-content">
                        <p><?php _e('Test PDF generation with all three templates (Modern, Classic, Minimal).', 'cfb-calculator'); ?></p>
                        <div class="cfb-template-tests">
                            <button type="button" class="button button-secondary cfb-test-pdf" data-template="modern">
                                <?php _e('Test Modern Template', 'cfb-calculator'); ?>
                            </button>
                            <button type="button" class="button button-secondary cfb-test-pdf" data-template="classic">
                                <?php _e('Test Classic Template', 'cfb-calculator'); ?>
                            </button>
                            <button type="button" class="button button-secondary cfb-test-pdf" data-template="minimal">
                                <?php _e('Test Minimal Template', 'cfb-calculator'); ?>
                            </button>
                        </div>
                        <div id="cfb-pdf-results" class="cfb-results-container" style="display: none;"></div>
                    </div>
                </div>

                <!-- Form Data Test -->
                <div class="cfb-diagnostic-section">
                    <h2><?php _e('Form Data Collection Test', 'cfb-calculator'); ?></h2>
                    <div class="cfb-diagnostic-content">
                        <p><?php _e('Test form data collection and storage functionality.', 'cfb-calculator'); ?></p>
                        <button type="button" class="button button-secondary" id="cfb-test-form-data">
                            <?php _e('Test Form Data', 'cfb-calculator'); ?>
                        </button>
                        <div id="cfb-form-data-results" class="cfb-results-container" style="display: none;"></div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="cfb-diagnostic-section">
                    <h2><?php _e('System Information', 'cfb-calculator'); ?></h2>
                    <div class="cfb-diagnostic-content">
                        <?php $this->render_system_info(); ?>
                    </div>
                </div>

                <!-- Migration Status -->
                <div class="cfb-diagnostic-section">
                    <h2><?php _e('Migration Status', 'cfb-calculator'); ?></h2>
                    <div class="cfb-diagnostic-content">
                        <?php $this->render_migration_status(); ?>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .cfb-diagnostics {
            margin: 20px 0;
        }
        .cfb-diagnostics-container {
            margin-top: 20px;
        }
        .cfb-diagnostic-section {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .cfb-diagnostic-section h2 {
            background: #f1f1f1;
            margin: 0;
            padding: 15px 20px;
            border-bottom: 1px solid #ccd0d4;
            font-size: 16px;
        }
        .cfb-diagnostic-content {
            padding: 20px;
        }
        .cfb-template-tests {
            margin: 15px 0;
        }
        .cfb-template-tests .button {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .cfb-results-container {
            margin-top: 15px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #0073aa;
        }
        .cfb-results-container.success {
            border-left-color: #46b450;
            background: #f0f8f0;
        }
        .cfb-results-container.error {
            border-left-color: #dc3232;
            background: #fdf0f0;
        }
        .cfb-results-container.warning {
            border-left-color: #ffb900;
            background: #fffbf0;
        }
        .cfb-system-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .cfb-info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .cfb-info-item:last-child {
            border-bottom: none;
        }
        .cfb-info-label {
            font-weight: 600;
            color: #555;
        }
        .cfb-info-value {
            color: #333;
        }
        .cfb-status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .cfb-status-success {
            background-color: #46b450;
        }
        .cfb-status-error {
            background-color: #dc3232;
        }
        .cfb-status-warning {
            background-color: #ffb900;
        }
        .cfb-loading {
            opacity: 0.6;
            pointer-events: none;
        }
        </style>

        <script>
        jQuery(document).ready(function($) {
            // Database check
            $('#cfb-check-database').on('click', function() {
                const button = $(this);
                const resultsContainer = $('#cfb-database-results');
                
                button.addClass('cfb-loading').text('<?php _e('Checking...', 'cfb-calculator'); ?>');
                resultsContainer.show().removeClass('success error warning').html('<p><?php _e('Running database checks...', 'cfb-calculator'); ?></p>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'cfb_run_database_check',
                        nonce: '<?php echo wp_create_nonce('cfb_diagnostics_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            resultsContainer.addClass('success').html(response.data.html);
                        } else {
                            resultsContainer.addClass('error').html('<p><strong><?php _e('Error:', 'cfb-calculator'); ?></strong> ' + response.data + '</p>');
                        }
                    },
                    error: function() {
                        resultsContainer.addClass('error').html('<p><strong><?php _e('Error:', 'cfb-calculator'); ?></strong> <?php _e('Failed to run database check', 'cfb-calculator'); ?></p>');
                    },
                    complete: function() {
                        button.removeClass('cfb-loading').text('<?php _e('Check Database', 'cfb-calculator'); ?>');
                    }
                });
            });

            // PDF generation test
            $('.cfb-test-pdf').on('click', function() {
                const button = $(this);
                const template = button.data('template');
                const resultsContainer = $('#cfb-pdf-results');
                
                button.addClass('cfb-loading').text('<?php _e('Testing...', 'cfb-calculator'); ?>');
                resultsContainer.show().removeClass('success error warning').html('<p><?php _e('Testing PDF generation...', 'cfb-calculator'); ?></p>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'cfb_test_pdf_generation',
                        template: template,
                        nonce: '<?php echo wp_create_nonce('cfb_diagnostics_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            resultsContainer.addClass('success').html(response.data.html);
                        } else {
                            resultsContainer.addClass('error').html('<p><strong><?php _e('Error:', 'cfb-calculator'); ?></strong> ' + response.data + '</p>');
                        }
                    },
                    error: function() {
                        resultsContainer.addClass('error').html('<p><strong><?php _e('Error:', 'cfb-calculator'); ?></strong> <?php _e('Failed to test PDF generation', 'cfb-calculator'); ?></p>');
                    },
                    complete: function() {
                        button.removeClass('cfb-loading').text('<?php _e('Test', 'cfb-calculator'); ?> ' + template.charAt(0).toUpperCase() + template.slice(1) + ' <?php _e('Template', 'cfb-calculator'); ?>');
                    }
                });
            });

            // Form data test
            $('#cfb-test-form-data').on('click', function() {
                const button = $(this);
                const resultsContainer = $('#cfb-form-data-results');
                
                button.addClass('cfb-loading').text('<?php _e('Testing...', 'cfb-calculator'); ?>');
                resultsContainer.show().removeClass('success error warning').html('<p><?php _e('Testing form data collection...', 'cfb-calculator'); ?></p>');
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'cfb_run_form_data_test',
                        nonce: '<?php echo wp_create_nonce('cfb_diagnostics_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            resultsContainer.addClass('success').html(response.data.html);
                        } else {
                            resultsContainer.addClass('error').html('<p><strong><?php _e('Error:', 'cfb-calculator'); ?></strong> ' + response.data + '</p>');
                        }
                    },
                    error: function() {
                        resultsContainer.addClass('error').html('<p><strong><?php _e('Error:', 'cfb-calculator'); ?></strong> <?php _e('Failed to test form data', 'cfb-calculator'); ?></p>');
                    },
                    complete: function() {
                        button.removeClass('cfb-loading').text('<?php _e('Test Form Data', 'cfb-calculator'); ?>');
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Render system information
     */
    private function render_system_info() {
        global $wpdb;
        
        $upload_dir = wp_upload_dir();
        $pdf_dir = $upload_dir['basedir'] . '/cfb-invoices/';
        
        $system_info = array(
            'WordPress Version' => get_bloginfo('version'),
            'PHP Version' => PHP_VERSION,
            'MySQL Version' => $wpdb->db_version(),
            'Plugin Version' => CFB_CALCULATOR_VERSION,
            'Database Version' => get_option('cfb_db_version', '1.0.0'),
            'Upload Directory' => $upload_dir['basedir'],
            'PDF Directory' => $pdf_dir,
            'PDF Directory Writable' => is_writable($pdf_dir) ? __('Yes', 'cfb-calculator') : __('No', 'cfb-calculator'),
            'TCPDF Available' => class_exists('TCPDF') ? __('Yes', 'cfb-calculator') : __('No', 'cfb-calculator'),
            'Memory Limit' => ini_get('memory_limit'),
            'Max Execution Time' => ini_get('max_execution_time') . 's',
            'Upload Max Filesize' => ini_get('upload_max_filesize'),
        );
        
        echo '<div class="cfb-system-info">';
        foreach ($system_info as $label => $value) {
            $status_class = '';
            if ($label === 'PDF Directory Writable' && $value === __('No', 'cfb-calculator')) {
                $status_class = 'cfb-status-error';
            } elseif ($label === 'TCPDF Available' && $value === __('No', 'cfb-calculator')) {
                $status_class = 'cfb-status-error';
            } elseif (in_array($label, array('PDF Directory Writable', 'TCPDF Available')) && $value === __('Yes', 'cfb-calculator')) {
                $status_class = 'cfb-status-success';
            }
            
            echo '<div class="cfb-info-item">';
            echo '<span class="cfb-info-label">' . esc_html($label) . ':</span>';
            echo '<span class="cfb-info-value">';
            if ($status_class) {
                echo '<span class="cfb-status-indicator ' . $status_class . '"></span>';
            }
            echo esc_html($value) . '</span>';
            echo '</div>';
        }
        echo '</div>';
    }

    /**
     * Render migration status
     */
    private function render_migration_status() {
        $last_migration = get_option('cfb_last_migration_version', '0.0.0');
        $db_version = get_option('cfb_db_version', '1.0.0');
        $current_version = CFB_CALCULATOR_VERSION;
        
        echo '<div class="cfb-system-info">';
        echo '<div class="cfb-info-item">';
        echo '<span class="cfb-info-label">' . __('Current Plugin Version', 'cfb-calculator') . ':</span>';
        echo '<span class="cfb-info-value">' . esc_html($current_version) . '</span>';
        echo '</div>';
        
        echo '<div class="cfb-info-item">';
        echo '<span class="cfb-info-label">' . __('Database Version', 'cfb-calculator') . ':</span>';
        echo '<span class="cfb-info-value">' . esc_html($db_version) . '</span>';
        echo '</div>';
        
        echo '<div class="cfb-info-item">';
        echo '<span class="cfb-info-label">' . __('Last Migration Version', 'cfb-calculator') . ':</span>';
        echo '<span class="cfb-info-value">' . esc_html($last_migration) . '</span>';
        echo '</div>';
        
        $migration_status = version_compare($last_migration, $current_version, '>=') ? 'up-to-date' : 'needs-update';
        $status_class = $migration_status === 'up-to-date' ? 'cfb-status-success' : 'cfb-status-warning';
        $status_text = $migration_status === 'up-to-date' ? __('Up to date', 'cfb-calculator') : __('Needs update', 'cfb-calculator');
        
        echo '<div class="cfb-info-item">';
        echo '<span class="cfb-info-label">' . __('Migration Status', 'cfb-calculator') . ':</span>';
        echo '<span class="cfb-info-value">';
        echo '<span class="cfb-status-indicator ' . $status_class . '"></span>';
        echo esc_html($status_text);
        echo '</span>';
        echo '</div>';
        echo '</div>';
        
        if ($migration_status === 'needs-update') {
            echo '<p><em>' . __('Note: Migrations will run automatically on the next admin page load.', 'cfb-calculator') . '</em></p>';
        }
    }

    /**
     * AJAX handler for database check
     */
    public function ajax_run_database_check() {
        check_ajax_referer('cfb_diagnostics_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }

        global $wpdb;
        $results = array();
        $all_good = true;

        // Check tables exist
        $tables = array(
            'cfb_forms' => $wpdb->prefix . 'cfb_forms',
            'cfb_form_fields' => $wpdb->prefix . 'cfb_form_fields',
            'cfb_form_submissions' => $wpdb->prefix . 'cfb_form_submissions',
            'cfb_variables' => $wpdb->prefix . 'cfb_variables',
            'cfb_invoices' => $wpdb->prefix . 'cfb_invoices',
            'cfb_invoice_items' => $wpdb->prefix . 'cfb_invoice_items'
        );

        foreach ($tables as $name => $table) {
            $exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'");
            if ($exists) {
                $results[] = "✅ Table {$name} exists";
            } else {
                $results[] = "❌ Table {$name} missing";
                $all_good = false;
            }
        }

        // Check form_data column specifically
        $invoices_table = $wpdb->prefix . 'cfb_invoices';
        $form_data_column = $wpdb->get_results("SHOW COLUMNS FROM {$invoices_table} LIKE 'form_data'");
        if (!empty($form_data_column)) {
            $results[] = "✅ form_data column exists in invoices table";
        } else {
            $results[] = "❌ form_data column missing from invoices table";
            $all_good = false;
        }

        // Check data counts
        if ($wpdb->get_var("SHOW TABLES LIKE '{$invoices_table}'")) {
            $invoice_count = $wpdb->get_var("SELECT COUNT(*) FROM {$invoices_table}");
            $form_data_count = $wpdb->get_var("SELECT COUNT(*) FROM {$invoices_table} WHERE form_data IS NOT NULL AND form_data != ''");
            $results[] = "📊 Total invoices: {$invoice_count}";
            $results[] = "📋 Invoices with form data: {$form_data_count}";
        }

        // Check submissions table
        $submissions_table = $wpdb->prefix . 'cfb_form_submissions';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$submissions_table}'")) {
            $submission_count = $wpdb->get_var("SELECT COUNT(*) FROM {$submissions_table}");
            $submission_data_count = $wpdb->get_var("SELECT COUNT(*) FROM {$submissions_table} WHERE submission_data IS NOT NULL AND submission_data != ''");
            $results[] = "📊 Total submissions: {$submission_count}";
            $results[] = "📋 Submissions with data: {$submission_data_count}";

            // Show sample submission data
            $sample_submission = $wpdb->get_row("SELECT submission_data FROM {$submissions_table} WHERE submission_data IS NOT NULL AND submission_data != '' LIMIT 1");
            if ($sample_submission) {
                $sample_data = json_decode($sample_submission->submission_data, true);
                if (!empty($sample_data)) {
                    $field_count = count($sample_data);
                    $results[] = "✅ Sample submission has {$field_count} fields";
                } else {
                    $results[] = "⚠️ Sample submission data is not valid JSON";
                }
            }
        }

        $html = '<h4>' . __('Database Check Results', 'cfb-calculator') . '</h4>';
        $html .= '<ul>';
        foreach ($results as $result) {
            $html .= '<li>' . esc_html($result) . '</li>';
        }
        $html .= '</ul>';

        if ($all_good) {
            $html .= '<p><strong>' . __('✅ All database checks passed!', 'cfb-calculator') . '</strong></p>';
        } else {
            $html .= '<p><strong>' . __('⚠️ Some issues found. The plugin will attempt to fix these automatically.', 'cfb-calculator') . '</strong></p>';
        }

        wp_send_json_success(array('html' => $html));
    }

    /**
     * AJAX handler for PDF generation test
     */
    public function ajax_test_pdf_generation() {
        check_ajax_referer('cfb_diagnostics_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }

        $template = sanitize_text_field($_POST['template']);

        if (!in_array($template, array('modern', 'classic', 'minimal'))) {
            wp_send_json_error(__('Invalid template', 'cfb-calculator'));
        }

        try {
            // Create test invoice data
            global $wpdb;
            $invoices_table = $wpdb->prefix . 'cfb_invoices';

            $test_data = array(
                'invoice_number' => 'TEST-' . time(),
                'form_id' => 1,
                'customer_name' => 'Test Customer',
                'customer_email' => '<EMAIL>',
                'customer_phone' => '******-0123',
                'customer_address' => '123 Test Street, Test City, TC 12345',
                'subtotal' => 1000.00,
                'tax_amount' => 100.00,
                'total_amount' => 1100.00,
                'currency' => 'USD',
                'status' => 'draft',
                'notes' => 'Test invoice for ' . $template . ' template',
                'form_data' => json_encode(array(
                    'project_type' => 'Website Development',
                    'team_size' => '3-5 people',
                    'duration' => '3-6 months',
                    'complexity' => 'Medium',
                    'budget_range' => '$10,000 - $25,000'
                ))
            );

            $insert_result = $wpdb->insert($invoices_table, $test_data);

            if ($insert_result === false) {
                wp_send_json_error(__('Failed to create test invoice', 'cfb-calculator'));
            }

            $test_invoice_id = $wpdb->insert_id;

            // Set template
            update_option('cfb_pdf_template', $template);

            // Generate PDF
            if (class_exists('CFB_PDF_Generator')) {
                $pdf_generator = CFB_PDF_Generator::get_instance();
                $invoice = $pdf_generator->get_invoice_data($test_invoice_id);

                if ($invoice) {
                    $pdf_path = $pdf_generator->generate_invoice_pdf($invoice);

                    if ($pdf_path) {
                        $upload_dir = wp_upload_dir();
                        $download_url = $upload_dir['baseurl'] . '/' . $pdf_path;

                        $html = '<h4>' . sprintf(__('%s Template Test Results', 'cfb-calculator'), ucfirst($template)) . '</h4>';
                        $html .= '<p>✅ ' . __('PDF generated successfully!', 'cfb-calculator') . '</p>';
                        $html .= '<p><a href="' . esc_url($download_url) . '" target="_blank" class="button button-primary">' . __('Download Test PDF', 'cfb-calculator') . '</a></p>';
                        $html .= '<p><small>' . sprintf(__('File: %s', 'cfb-calculator'), esc_html($pdf_path)) . '</small></p>';

                        // Clean up test data
                        $wpdb->delete($invoices_table, array('id' => $test_invoice_id), array('%d'));

                        wp_send_json_success(array('html' => $html));
                    } else {
                        wp_send_json_error(__('PDF generation failed', 'cfb-calculator'));
                    }
                } else {
                    wp_send_json_error(__('Could not retrieve test invoice data', 'cfb-calculator'));
                }
            } else {
                wp_send_json_error(__('CFB_PDF_Generator class not found', 'cfb-calculator'));
            }

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * AJAX handler for form data test
     */
    public function ajax_run_form_data_test() {
        check_ajax_referer('cfb_diagnostics_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }

        global $wpdb;
        $invoices_table = $wpdb->prefix . 'cfb_invoices';

        // Test form data
        $test_form_data = array(
            'project_type' => 'Website Development',
            'team_size' => '3-5 people',
            'duration' => '3-6 months',
            'complexity' => 'Medium to High',
            'budget_range' => '$10,000 - $25,000',
            'features' => array('E-commerce', 'CMS', 'Mobile Responsive'),
            'hosting_required' => 'Yes',
            'maintenance_plan' => 'Premium Support'
        );

        $test_data = array(
            'invoice_number' => 'FORM-TEST-' . time(),
            'form_id' => 1,
            'customer_name' => 'Form Data Test User',
            'customer_email' => '<EMAIL>',
            'subtotal' => 1500.00,
            'tax_amount' => 150.00,
            'total_amount' => 1650.00,
            'form_data' => json_encode($test_form_data)
        );

        $insert_result = $wpdb->insert($invoices_table, $test_data);

        if ($insert_result !== false) {
            $test_id = $wpdb->insert_id;

            // Retrieve and verify
            $retrieved = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$invoices_table} WHERE id = %d", $test_id));

            if ($retrieved && !empty($retrieved->form_data)) {
                $decoded_data = json_decode($retrieved->form_data, true);

                $html = '<h4>' . __('Form Data Test Results', 'cfb-calculator') . '</h4>';
                $html .= '<p>✅ ' . __('Form data saved successfully!', 'cfb-calculator') . '</p>';
                $html .= '<p>✅ ' . __('Form data retrieved successfully!', 'cfb-calculator') . '</p>';
                $html .= '<p>✅ ' . __('JSON encoding/decoding works correctly!', 'cfb-calculator') . '</p>';
                $html .= '<h5>' . __('Test Data:', 'cfb-calculator') . '</h5>';
                $html .= '<pre>' . esc_html(json_encode($decoded_data, JSON_PRETTY_PRINT)) . '</pre>';

                // Clean up
                $wpdb->delete($invoices_table, array('id' => $test_id), array('%d'));

                wp_send_json_success(array('html' => $html));
            } else {
                wp_send_json_error(__('Form data not properly saved or retrieved', 'cfb-calculator'));
            }
        } else {
            wp_send_json_error(__('Failed to save test form data', 'cfb-calculator'));
        }
    }
}
