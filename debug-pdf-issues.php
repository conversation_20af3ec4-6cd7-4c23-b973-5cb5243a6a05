<?php
/**
 * Debug PDF Issues Script
 * 
 * This script helps debug the three main PDF issues:
 * 1. Form data not appearing
 * 2. Currency formatting wrong (numbers reversed)
 * 3. Logo positioning incorrect
 */

// WordPress environment
require_once('../../../wp-config.php');

echo "<h1>CFB Calculator PDF Issues Debug</h1>";

global $wpdb;

// 1. Check form_data column and test data
echo "<h2>1. Form Data Issue Debug</h2>";

$invoices_table = $wpdb->prefix . 'cfb_invoices';

// Check if form_data column exists
$column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$invoices_table} LIKE 'form_data'");
if (!empty($column_exists)) {
    echo "<p style='color: green;'>✅ form_data column exists</p>";
} else {
    echo "<p style='color: red;'>❌ form_data column missing - adding it now...</p>";
    $result = $wpdb->query("ALTER TABLE {$invoices_table} ADD COLUMN form_data longtext AFTER notes");
    if ($result !== false) {
        echo "<p style='color: green;'>✅ form_data column added successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to add form_data column: " . $wpdb->last_error . "</p>";
    }
}

// Check existing invoices for form_data
$invoices_with_data = $wpdb->get_results("SELECT id, invoice_number, form_data FROM {$invoices_table} WHERE form_data IS NOT NULL AND form_data != '' LIMIT 5");
echo "<h3>Existing Invoices with Form Data:</h3>";
if (!empty($invoices_with_data)) {
    foreach ($invoices_with_data as $invoice) {
        echo "<p><strong>Invoice #{$invoice->invoice_number}:</strong></p>";
        if ($invoice->form_data) {
            $form_data = json_decode($invoice->form_data, true);
            if ($form_data) {
                echo "<ul>";
                foreach ($form_data as $field => $value) {
                    echo "<li><strong>{$field}:</strong> {$value}</li>";
                }
                echo "</ul>";
            } else {
                echo "<p>Form data exists but couldn't be decoded: " . htmlspecialchars($invoice->form_data) . "</p>";
            }
        }
    }
} else {
    echo "<p style='color: orange;'>⚠️ No invoices found with form data</p>";
}

// 2. Test currency formatting
echo "<h2>2. Currency Formatting Debug</h2>";

// Test the format_currency_farsi function logic
$test_amount = 6105000;

echo "<h3>Testing Currency Formatting Logic:</h3>";

// Simulate RTL mode
echo "<h4>RTL Mode (Persian):</h4>";
$rtl_support = true;
$formatted_amount = number_format($test_amount, 2);
echo "<p><strong>Original amount:</strong> {$test_amount}</p>";
echo "<p><strong>After number_format:</strong> {$formatted_amount}</p>";

// Convert to Farsi digits
$english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
$farsi_digits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
$farsi_formatted = str_replace($english_digits, $farsi_digits, $formatted_amount);
echo "<p><strong>After Farsi conversion:</strong> {$farsi_formatted}</p>";

$currency_symbol = 'تومان';
$final_rtl = $farsi_formatted . ' ' . $currency_symbol;
echo "<p><strong>Final RTL format:</strong> {$final_rtl}</p>";

// Simulate LTR mode
echo "<h4>LTR Mode (English):</h4>";
$rtl_support = false;
$formatted_amount = number_format($test_amount, 2);
$currency_symbol = '$';
$final_ltr = $currency_symbol . ' ' . $formatted_amount;
echo "<p><strong>Final LTR format:</strong> {$final_ltr}</p>";

// 3. Test logo positioning logic
echo "<h2>3. Logo Positioning Debug</h2>";

$logo_url = get_option('cfb_company_logo', '');
echo "<p><strong>Logo URL from settings:</strong> " . ($logo_url ? $logo_url : 'Not set') . "</p>";

if ($logo_url) {
    // Test URL to path conversion
    $upload_dir = wp_upload_dir();
    $logo_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $logo_url);
    echo "<p><strong>Logo path:</strong> {$logo_path}</p>";
    echo "<p><strong>Logo exists:</strong> " . (file_exists($logo_path) ? 'YES' : 'NO') . "</p>";
    
    // Test positioning logic
    echo "<h3>Logo Positioning Logic:</h3>";
    $page_width = 210; // A4 width in mm
    
    echo "<h4>RTL Mode:</h4>";
    $rtl_x = $page_width - 55;
    echo "<p><strong>RTL Logo X position:</strong> {$rtl_x}mm (should be on RIGHT side)</p>";
    
    echo "<h4>LTR Mode:</h4>";
    $ltr_x = 25;
    echo "<p><strong>LTR Logo X position:</strong> {$ltr_x}mm (should be on LEFT side)</p>";
}

// 4. Test PDF settings
echo "<h2>4. PDF Settings Debug</h2>";

$pdf_settings = array(
    'cfb_pdf_rtl_support' => get_option('cfb_pdf_rtl_support', 0),
    'cfb_convert_to_farsi_digits' => get_option('cfb_convert_to_farsi_digits', 0),
    'cfb_currency_symbol' => get_option('cfb_currency_symbol', '$'),
    'cfb_currency_position' => get_option('cfb_currency_position', 'left'),
    'cfb_decimal_places' => get_option('cfb_decimal_places', 2),
    'cfb_pdf_template' => get_option('cfb_pdf_template', 'modern'),
    'cfb_company_name' => get_option('cfb_company_name', ''),
    'cfb_company_logo' => get_option('cfb_company_logo', '')
);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
foreach ($pdf_settings as $setting => $value) {
    $display_value = is_bool($value) ? ($value ? 'TRUE' : 'FALSE') : $value;
    echo "<tr><td>{$setting}</td><td>{$display_value}</td></tr>";
}
echo "</table>";

// 5. Create test invoice with form data
echo "<h2>5. Create Test Invoice with Form Data</h2>";

$test_form_data = array(
    'project_name' => 'Test Website Project',
    'project_type' => 'E-commerce Website',
    'team_size' => '3 developers',
    'duration' => '4 months',
    'complexity' => 'Medium',
    'features' => array('Payment Gateway', 'User Registration', 'Admin Panel')
);

$test_invoice_data = array(
    'invoice_number' => 'DEBUG-' . date('YmdHis'),
    'form_id' => 1,
    'submission_id' => null,
    'customer_name' => 'احمد محمدی (Test Customer)',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '۰۹۱۲۳۴۵۶۷۸۹',
    'customer_address' => 'تهران، خیابان آزادی',
    'subtotal' => 6105000.00,
    'tax_amount' => 610500.00,
    'total_amount' => 6715500.00,
    'currency' => 'تومان',
    'status' => 'draft',
    'notes' => 'Test invoice for debugging PDF issues',
    'form_data' => wp_json_encode($test_form_data)
);

echo "<p><strong>Test Form Data to Save:</strong></p>";
echo "<pre>" . print_r($test_form_data, true) . "</pre>";

$result = $wpdb->insert(
    $invoices_table,
    $test_invoice_data,
    array('%s', '%d', '%d', '%s', '%s', '%s', '%s', '%f', '%f', '%f', '%s', '%s', '%s', '%s')
);

if ($result) {
    $test_invoice_id = $wpdb->insert_id;
    echo "<p style='color: green;'>✅ Test invoice created with ID: {$test_invoice_id}</p>";
    echo "<p><strong>Invoice Number:</strong> {$test_invoice_data['invoice_number']}</p>";
    
    // Retrieve and verify
    $saved_invoice = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$invoices_table} WHERE id = %d", $test_invoice_id));
    if ($saved_invoice && $saved_invoice->form_data) {
        $saved_form_data = json_decode($saved_invoice->form_data, true);
        echo "<p style='color: green;'>✅ Form data saved and retrieved successfully:</p>";
        echo "<ul>";
        foreach ($saved_form_data as $field => $value) {
            if (is_array($value)) {
                $value = implode(', ', $value);
            }
            echo "<li><strong>{$field}:</strong> {$value}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ Form data not saved properly</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Failed to create test invoice: " . $wpdb->last_error . "</p>";
}

// 6. Summary and recommendations
echo "<h2>6. Summary and Recommendations</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<h3>🔍 Debugging Results:</h3>";

echo "<h4>Form Data Issue:</h4>";
if (!empty($column_exists)) {
    echo "<p style='color: green;'>✅ Database column exists - form data should be saved</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Database column was missing but has been added</p>";
}

echo "<h4>Currency Formatting:</h4>";
echo "<p>✅ Logic appears correct - RTL should show: ۶,۱۰۵,۰۰۰.۰۰ تومان</p>";
echo "<p>✅ LTR should show: $6,105,000.00</p>";

echo "<h4>Logo Positioning:</h4>";
if ($logo_url) {
    echo "<p>✅ Logo URL is set - RTL should position at X=" . ($page_width - 55) . "mm (RIGHT side)</p>";
    echo "<p>✅ LTR should position at X=25mm (LEFT side)</p>";
} else {
    echo "<p style='color: orange;'>⚠️ No logo URL set in settings</p>";
}

echo "<h3>📋 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Test PDF Generation:</strong> Use the test invoice created above (ID: " . (isset($test_invoice_id) ? $test_invoice_id : 'N/A') . ")</li>";
echo "<li><strong>Check RTL Settings:</strong> Ensure RTL support is enabled in PDF settings</li>";
echo "<li><strong>Verify Logo Upload:</strong> Make sure logo is uploaded and accessible</li>";
echo "<li><strong>Test Both Templates:</strong> Try both Modern and Minimal templates</li>";
echo "</ol>";

echo "<h3>🛠️ If Issues Persist:</h3>";
echo "<ul>";
echo "<li>Check browser console for JavaScript errors during invoice creation</li>";
echo "<li>Check WordPress error logs for PHP errors during PDF generation</li>";
echo "<li>Verify TCPDF library is working correctly</li>";
echo "<li>Test with a simple form to isolate the issue</li>";
echo "</ul>";
echo "</div>";

// Clean up test data
if (isset($test_invoice_id)) {
    echo "<h2>7. Cleanup</h2>";
    echo "<p><strong>Test invoice created with ID {$test_invoice_id} for testing.</strong></p>";
    echo "<p>You can delete it manually from the invoices page or generate a PDF to test the fixes.</p>";
}

echo "<p style='color: green; font-weight: bold; margin-top: 30px;'>🎉 PDF Issues Debug Completed!</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style>
