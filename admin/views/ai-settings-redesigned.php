<?php
/**
 * Redesigned AI Settings Page
 * Enhanced formula builder with better debugging and testing capabilities
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$ai_settings = get_option('cfb_ai_settings', array());
$formula_engine = CFB_Formula_Engine::get_instance();

// Handle form submission
if (isset($_POST['save_ai_settings'])) {
    $ai_settings = array(
        'enabled' => isset($_POST['ai_enabled']) ? 1 : 0,
        'api_key' => sanitize_text_field($_POST['api_key']),
        'model' => sanitize_text_field($_POST['model']),
        'temperature' => floatval($_POST['temperature']),
        'max_tokens' => intval($_POST['max_tokens']),
        'formula_suggestions' => isset($_POST['formula_suggestions']) ? 1 : 0,
        'auto_optimize' => isset($_POST['auto_optimize']) ? 1 : 0
    );
    
    update_option('cfb_ai_settings', $ai_settings);
    echo '<div class="notice notice-success"><p>' . __('AI Settings saved successfully!', 'cfb-calculator') . '</p></div>';
}

// Handle formula testing
$test_result = '';
if (isset($_POST['test_formula'])) {
    $test_formula = sanitize_text_field($_POST['test_formula']);
    $test_variables = array();
    
    // Parse test variables
    if (!empty($_POST['test_variables'])) {
        $vars = explode("\n", $_POST['test_variables']);
        foreach ($vars as $var) {
            if (strpos($var, '=') !== false) {
                list($name, $value) = explode('=', $var, 2);
                $test_variables[trim($name)] = floatval(trim($value));
            }
        }
    }
    
    // Set test variables in formula engine
    $reflection = new ReflectionClass($formula_engine);
    $property = $reflection->getProperty('variables');
    $property->setAccessible(true);
    $property->setValue($formula_engine, $test_variables);
    
    try {
        $result = $formula_engine->evaluate_formula($test_formula);
        $test_result = "✅ Result: " . number_format($result, 2);
    } catch (Exception $e) {
        $test_result = "❌ Error: " . $e->getMessage();
    }
}
?>

<div class="wrap cfb-ai-settings">
    <h1><?php _e('🤖 CFB Calculator - AI Settings & Formula Builder', 'cfb-calculator'); ?></h1>

    <div class="cfb-settings-container">
        <!-- Navigation Tabs -->
        <nav class="nav-tab-wrapper">
            <a href="#ai-config" class="nav-tab nav-tab-active"><?php _e('AI Configuration', 'cfb-calculator'); ?></a>
            <a href="#formula-builder" class="nav-tab"><?php _e('Formula Builder', 'cfb-calculator'); ?></a>
            <a href="#formula-tester" class="nav-tab"><?php _e('Formula Tester', 'cfb-calculator'); ?></a>
            <a href="#variables-manager" class="nav-tab"><?php _e('Variables Manager', 'cfb-calculator'); ?></a>
            <a href="#debugging" class="nav-tab"><?php _e('Debugging Tools', 'cfb-calculator'); ?></a>
        </nav>

        <form method="post" action="">
            <?php wp_nonce_field('cfb_ai_settings', 'cfb_ai_nonce'); ?>
            
            <!-- AI Configuration Tab -->
            <div id="ai-config" class="tab-content active">
                <div class="cfb-section">
                    <h2><?php _e('🔧 AI Configuration', 'cfb-calculator'); ?></h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable AI Features', 'cfb-calculator'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="ai_enabled" value="1" <?php checked(isset($ai_settings['enabled']) ? $ai_settings['enabled'] : 0, 1); ?>>
                                    <?php _e('Enable AI-powered formula suggestions and optimization', 'cfb-calculator'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('API Key', 'cfb-calculator'); ?></th>
                            <td>
                                <input type="password" name="api_key" value="<?php echo esc_attr($ai_settings['api_key'] ?? ''); ?>" class="regular-text">
                                <p class="description"><?php _e('Enter your OpenAI API key for AI features', 'cfb-calculator'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('AI Model', 'cfb-calculator'); ?></th>
                            <td>
                                <select name="model">
                                    <option value="gpt-3.5-turbo" <?php selected($ai_settings['model'] ?? '', 'gpt-3.5-turbo'); ?>>GPT-3.5 Turbo</option>
                                    <option value="gpt-4" <?php selected($ai_settings['model'] ?? '', 'gpt-4'); ?>>GPT-4</option>
                                    <option value="gpt-4-turbo" <?php selected($ai_settings['model'] ?? '', 'gpt-4-turbo'); ?>>GPT-4 Turbo</option>
                                </select>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Temperature', 'cfb-calculator'); ?></th>
                            <td>
                                <input type="range" name="temperature" min="0" max="1" step="0.1" value="<?php echo esc_attr($ai_settings['temperature'] ?? 0.3); ?>" class="cfb-range">
                                <span class="range-value"><?php echo esc_attr($ai_settings['temperature'] ?? 0.3); ?></span>
                                <p class="description"><?php _e('Controls randomness in AI responses (0 = deterministic, 1 = creative)', 'cfb-calculator'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Max Tokens', 'cfb-calculator'); ?></th>
                            <td>
                                <input type="number" name="max_tokens" value="<?php echo esc_attr($ai_settings['max_tokens'] ?? 1000); ?>" min="100" max="4000" class="small-text">
                                <p class="description"><?php _e('Maximum tokens for AI responses', 'cfb-calculator'); ?></p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Formula Suggestions', 'cfb-calculator'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="formula_suggestions" value="1" <?php checked(isset($ai_settings['formula_suggestions']) ? $ai_settings['formula_suggestions'] : 0, 1); ?>>
                                    <?php _e('Enable AI formula suggestions in form builder', 'cfb-calculator'); ?>
                                </label>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('Auto Optimization', 'cfb-calculator'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="auto_optimize" value="1" <?php checked(isset($ai_settings['auto_optimize']) ? $ai_settings['auto_optimize'] : 0, 1); ?>>
                                    <?php _e('Automatically optimize formulas for better performance', 'cfb-calculator'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Formula Builder Tab -->
            <div id="formula-builder" class="tab-content">
                <div class="cfb-section">
                    <h2>🧮 Advanced Formula Builder</h2>
                    
                    <div class="formula-builder-container">
                        <div class="formula-input-section">
                            <h3>Formula Input</h3>
                            <textarea id="formula-input" name="formula" rows="4" class="large-text code" placeholder="Enter your formula here...
Example: 5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))"><?php echo esc_textarea($_POST['formula'] ?? ''); ?></textarea>
                            
                            <div class="formula-tools">
                                <button type="button" class="button" onclick="insertFunction('max()')">max()</button>
                                <button type="button" class="button" onclick="insertFunction('min()')">min()</button>
                                <button type="button" class="button" onclick="insertFunction('ceil()')">ceil()</button>
                                <button type="button" class="button" onclick="insertFunction('floor()')">floor()</button>
                                <button type="button" class="button" onclick="insertFunction('round()')">round()</button>
                                <button type="button" class="button" onclick="insertFunction('abs()')">abs()</button>
                                <button type="button" class="button" onclick="insertFunction('sqrt()')">sqrt()</button>
                                <button type="button" class="button" onclick="insertFunction('pow(,)')">pow()</button>
                            </div>
                        </div>
                        
                        <div class="variables-section">
                            <h3>Available Variables</h3>
                            <div id="variables-list">
                                <div class="variable-category">
                                    <h4>📋 Form Fields (Use these in formulas)</h4>
                                    <?php
                                    global $wpdb;

                                    // Get form fields from the most recent form
                                    $recent_form = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}cfb_forms ORDER BY id DESC LIMIT 1");
                                    if ($recent_form) {
                                        $form_data = json_decode($recent_form->form_data, true);
                                        if (isset($form_data['fields'])) {
                                            foreach ($form_data['fields'] as $field) {
                                                if (isset($field['name']) && isset($field['type'])) {
                                                    $field_name = $field['name'];
                                                    $field_label = $field['label'] ?? $field_name;
                                                    $field_type = $field['type'];
                                                    $default_value = $field['value'] ?? $field['default_value'] ?? '0';

                                                    echo '<div class="variable-item form-field" onclick="insertVariable(\'{' . esc_js($field_name) . '}\')">';
                                                    echo '<strong>{' . esc_html($field_name) . '}</strong>';
                                                    echo '<span class="variable-label">' . esc_html($field_label) . ' (' . esc_html($field_type) . ')</span>';
                                                    echo '<span class="variable-value">Default: ' . esc_html($default_value) . '</span>';
                                                    echo '</div>';
                                                }
                                            }
                                        }
                                    }
                                    ?>
                                </div>

                                <div class="variable-category">
                                    <h4>🌐 Global Variables (For reference only)</h4>
                                    <?php
                                    $global_variables = $wpdb->get_results("SELECT name, label, value FROM {$wpdb->prefix}cfb_variables WHERE is_active = 1");
                                    if ($global_variables) {
                                        foreach ($global_variables as $var) {
                                            echo '<div class="variable-item global-var" onclick="showGlobalVarWarning(\'' . esc_js($var->name) . '\')">';
                                            echo '<strong>{' . esc_html($var->name) . '}</strong>';
                                            echo '<span class="variable-label">' . esc_html($var->label) . '</span>';
                                            echo '<span class="variable-value">= ' . esc_html($var->value) . '</span>';
                                            echo '<span class="variable-warning">⚠️ Create form field to use</span>';
                                            echo '</div>';
                                        }
                                    } else {
                                        echo '<p class="no-variables">No global variables found. <a href="#variables-manager" class="nav-tab-link">Create variables</a></p>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="formula-preview">
                            <h3>Formula Preview</h3>
                            <div id="formula-preview-content" class="formula-display">
                                Enter a formula to see the preview...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Formula Tester Tab -->
            <div id="formula-tester" class="tab-content">
                <div class="cfb-section">
                    <h2>🧪 Formula Tester</h2>
                    
                    <div class="tester-container">
                        <div class="test-input">
                            <h3>Test Formula</h3>
                            <textarea name="test_formula" rows="3" class="large-text code" placeholder="Enter formula to test..."><?php echo esc_textarea($_POST['test_formula'] ?? ''); ?></textarea>
                            
                            <h3>Test Variables</h3>
                            <textarea name="test_variables" rows="5" class="large-text code" placeholder="Enter variables in format:
dropdown_4 = 6000
price = 10.50
tax_rate = 0.1"><?php echo esc_textarea($_POST['test_variables'] ?? ''); ?></textarea>
                            
                            <p class="submit">
                                <input type="submit" name="test_formula" class="button-primary" value="🧪 Test Formula">
                            </p>
                        </div>
                        
                        <div class="test-result">
                            <h3>Test Result</h3>
                            <div class="result-display">
                                <?php if (!empty($test_result)): ?>
                                    <div class="test-output"><?php echo esc_html($test_result); ?></div>
                                <?php else: ?>
                                    <p class="no-result">Enter a formula and click "Test Formula" to see results.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="test-examples">
                        <h3>📚 Formula Examples</h3>
                        <div class="examples-grid">
                            <div class="example-item">
                                <h4>Basic Calculation</h4>
                                <code>{price} * {quantity}</code>
                                <p>Simple multiplication</p>
                            </div>
                            <div class="example-item">
                                <h4>With Tax</h4>
                                <code>{price} * {quantity} * (1 + {tax_rate})</code>
                                <p>Price with tax calculation</p>
                            </div>
                            <div class="example-item">
                                <h4>Tiered Pricing</h4>
                                <code>5000000*(1+0.2*max(0,ceil(({dropdown_4}+100-5000)/1000)))</code>
                                <p>Complex tiered pricing formula</p>
                            </div>
                            <div class="example-item">
                                <h4>Discount Calculation</h4>
                                <code>{price} * {quantity} * (1 - min({discount_rate}, 0.5))</code>
                                <p>Price with maximum 50% discount</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Variables Manager Tab -->
            <div id="variables-manager" class="tab-content">
                <div class="cfb-section">
                    <h2>📊 Variables Manager</h2>

                    <div class="variables-manager-container">
                        <div class="add-variable-section">
                            <h3>Add New Variable</h3>
                            <div class="variable-form">
                                <input type="text" id="new-var-name" placeholder="Variable name (e.g., dropdown_4)" class="regular-text">
                                <input type="text" id="new-var-label" placeholder="Display label" class="regular-text">
                                <input type="number" id="new-var-value" placeholder="Default value" step="0.01" class="small-text">
                                <button type="button" class="button-primary" onclick="addVariable()">➕ Add Variable</button>
                            </div>
                        </div>

                        <div class="variables-list-section">
                            <h3>Existing Variables</h3>
                            <div class="variables-table-container">
                                <table class="wp-list-table widefat fixed striped">
                                    <thead>
                                        <tr>
                                            <th>Variable Name</th>
                                            <th>Label</th>
                                            <th>Value</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="variables-table-body">
                                        <?php
                                        global $wpdb;
                                        $all_variables = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}cfb_variables ORDER BY name");
                                        if ($all_variables) {
                                            foreach ($all_variables as $var) {
                                                echo '<tr data-id="' . $var->id . '">';
                                                echo '<td><code>{' . esc_html($var->name) . '}</code></td>';
                                                echo '<td>' . esc_html($var->label) . '</td>';
                                                echo '<td><input type="number" value="' . esc_attr($var->value) . '" step="0.01" class="small-text var-value" data-id="' . $var->id . '"></td>';
                                                echo '<td><span class="status-badge ' . ($var->is_active ? 'active' : 'inactive') . '">' . ($var->is_active ? 'Active' : 'Inactive') . '</span></td>';
                                                echo '<td>';
                                                echo '<button type="button" class="button button-small" onclick="toggleVariable(' . $var->id . ')">' . ($var->is_active ? 'Deactivate' : 'Activate') . '</button> ';
                                                echo '<button type="button" class="button button-small button-link-delete" onclick="deleteVariable(' . $var->id . ')">Delete</button>';
                                                echo '</td>';
                                                echo '</tr>';
                                            }
                                        } else {
                                            echo '<tr><td colspan="5" class="no-items">No variables found. Add your first variable above.</td></tr>';
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Debugging Tools Tab -->
            <div id="debugging" class="tab-content">
                <div class="cfb-section">
                    <h2>🔧 Debugging Tools</h2>

                    <div class="debugging-container">
                        <div class="debug-section">
                            <h3>🔍 Formula Debugger</h3>
                            <p>Enter a formula to see step-by-step debugging information:</p>

                            <textarea id="debug-formula" rows="3" class="large-text code" placeholder="Enter formula to debug..."></textarea>
                            <button type="button" class="button-primary" onclick="debugFormula()">🔍 Debug Formula</button>

                            <div id="debug-output" class="debug-output"></div>
                        </div>

                        <div class="system-info-section">
                            <h3>⚙️ System Information</h3>
                            <div class="system-info">
                                <div class="info-item">
                                    <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?>
                                </div>
                                <div class="info-item">
                                    <strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?>
                                </div>
                                <div class="info-item">
                                    <strong>CFB Plugin Version:</strong> <?php echo defined('CFB_VERSION') ? CFB_VERSION : 'Unknown'; ?>
                                </div>
                                <div class="info-item">
                                    <strong>Available Functions:</strong>
                                    <div class="functions-list">
                                        <?php
                                        $reflection = new ReflectionClass($formula_engine);
                                        $functions_property = $reflection->getProperty('functions');
                                        $functions_property->setAccessible(true);
                                        $functions = $functions_property->getValue($formula_engine);
                                        foreach ($functions as $func_name => $func) {
                                            echo '<span class="function-tag">' . esc_html($func_name) . '()</span> ';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <strong>Database Tables:</strong>
                                    <div class="tables-list">
                                        <?php
                                        $tables = array(
                                            $wpdb->prefix . 'cfb_forms' => 'Forms',
                                            $wpdb->prefix . 'cfb_variables' => 'Variables',
                                            $wpdb->prefix . 'cfb_submissions' => 'Submissions'
                                        );
                                        foreach ($tables as $table => $label) {
                                            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
                                            echo '<span class="table-status ' . ($exists ? 'exists' : 'missing') . '">' . $label . '</span> ';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="error-logs-section">
                            <h3>📋 Recent Error Logs</h3>
                            <div class="error-logs">
                                <?php
                                // Try to read recent PHP error logs
                                $error_log_paths = array(
                                    '/Applications/XAMPP/xamppfiles/logs/php_error_log',
                                    ini_get('error_log'),
                                    ABSPATH . 'wp-content/debug.log'
                                );

                                $recent_errors = array();
                                foreach ($error_log_paths as $log_path) {
                                    if (file_exists($log_path) && is_readable($log_path)) {
                                        $lines = file($log_path);
                                        $cfb_lines = array_filter($lines, function($line) {
                                            return strpos($line, 'CFB:') !== false;
                                        });
                                        $recent_errors = array_merge($recent_errors, array_slice($cfb_lines, -10));
                                        break;
                                    }
                                }

                                if (!empty($recent_errors)) {
                                    echo '<div class="error-log-content">';
                                    foreach (array_reverse($recent_errors) as $error) {
                                        echo '<div class="log-entry">' . esc_html($error) . '</div>';
                                    }
                                    echo '</div>';
                                } else {
                                    echo '<p class="no-errors">No recent CFB errors found in logs.</p>';
                                }
                                ?>
                            </div>
                        </div>

                        <div class="quick-tests-section">
                            <h3>⚡ Quick Tests</h3>
                            <div class="quick-tests">
                                <button type="button" class="button" onclick="testBasicMath()">Test Basic Math</button>
                                <button type="button" class="button" onclick="testFunctions()">Test Functions</button>
                                <button type="button" class="button" onclick="testVariables()">Test Variables</button>
                                <button type="button" class="button" onclick="clearDebugOutput()">Clear Output</button>
                            </div>
                            <div id="quick-test-output" class="quick-test-output"></div>
                        </div>
                    </div>
                </div>
            </div>

            <p class="submit">
                <input type="submit" name="save_ai_settings" class="button-primary" value="💾 Save AI Settings">
            </p>
        </form>
    </div>
</div>

<style>
.cfb-ai-settings {
    max-width: 1200px;
}

.cfb-settings-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin-top: 20px;
}

.nav-tab-wrapper {
    border-bottom: 1px solid #ccd0d4;
    margin: 0;
    padding: 0 20px;
    background: #f9f9f9;
}

.nav-tab {
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: #555;
    padding: 15px 20px;
    text-decoration: none;
    font-weight: 600;
}

.nav-tab:hover {
    color: #0073aa;
}

.nav-tab.nav-tab-active {
    color: #0073aa;
    border-bottom-color: #0073aa;
    background: #fff;
}

.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

.cfb-section {
    margin-bottom: 30px;
}

.cfb-section h2 {
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    color: #0073aa;
}

.formula-builder-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    margin-top: 20px;
}

.formula-input-section {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 4px;
}

.formula-tools {
    margin-top: 10px;
}

.formula-tools .button {
    margin-right: 5px;
    margin-bottom: 5px;
}

.variables-section {
    background: #f0f8ff;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #0073aa;
}

.variable-item {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 5px;
    cursor: pointer;
    background: #fff;
    transition: background 0.2s;
}

.variable-item:hover {
    background: #e6f3ff;
}

.variable-item.form-field {
    border-left: 4px solid #28a745;
}

.variable-item.global-var {
    border-left: 4px solid #ffc107;
    opacity: 0.7;
}

.variable-category {
    margin-bottom: 20px;
}

.variable-category h4 {
    margin: 0 0 10px 0;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 13px;
}

.variable-label {
    display: block;
    font-size: 12px;
    color: #666;
}

.variable-value {
    display: block;
    font-size: 11px;
    color: #0073aa;
    font-weight: bold;
}

.variable-warning {
    display: block;
    font-size: 10px;
    color: #dc3545;
    font-weight: bold;
    margin-top: 2px;
}

.formula-preview {
    grid-column: 1 / -1;
    background: #fff8e1;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #ffc107;
    margin-top: 20px;
}

.formula-display {
    font-family: 'Courier New', monospace;
    background: #fff;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    min-height: 50px;
}

.tester-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.test-input, .test-result {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 4px;
}

.result-display {
    background: #fff;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    min-height: 100px;
}

.test-output {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.example-item {
    background: #f0f8ff;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #0073aa;
}

.example-item h4 {
    margin: 0 0 10px 0;
    color: #0073aa;
}

.example-item code {
    display: block;
    background: #fff;
    padding: 8px;
    border-radius: 3px;
    margin: 5px 0;
    font-size: 12px;
    word-break: break-all;
}

.cfb-range {
    width: 200px;
}

.range-value {
    margin-left: 10px;
    font-weight: bold;
}

.no-variables, .no-result {
    color: #666;
    font-style: italic;
}

/* Variables Manager Styles */
.variables-manager-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.add-variable-section {
    background: #f0f8ff;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #0073aa;
}

.variable-form {
    display: grid;
    grid-template-columns: 1fr 1fr 150px auto;
    gap: 10px;
    align-items: center;
}

.variables-table-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Debugging Tools Styles */
.debugging-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.debug-section, .system-info-section, .error-logs-section, .quick-tests-section {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.debug-output, .quick-test-output {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
    min-height: 100px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.system-info {
    display: grid;
    gap: 15px;
}

.info-item {
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.functions-list, .tables-list {
    margin-top: 5px;
}

.function-tag {
    background: #e1f5fe;
    color: #0277bd;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-right: 5px;
    font-family: 'Courier New', monospace;
}

.table-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-right: 5px;
    font-weight: bold;
}

.table-status.exists {
    background: #d4edda;
    color: #155724;
}

.table-status.missing {
    background: #f8d7da;
    color: #721c24;
}

.error-log-content {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.log-entry {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
    color: #666;
}

.log-entry:last-child {
    border-bottom: none;
}

.quick-tests {
    margin-bottom: 15px;
}

.quick-tests .button {
    margin-right: 10px;
    margin-bottom: 5px;
}

.no-errors, .no-items {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

@media (max-width: 768px) {
    .formula-builder-container,
    .tester-container {
        grid-template-columns: 1fr;
    }

    .examples-grid {
        grid-template-columns: 1fr;
    }

    .variable-form {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Tab switching
    $('.nav-tab').click(function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs and content
        $('.nav-tab').removeClass('nav-tab-active');
        $('.tab-content').removeClass('active');
        
        // Add active class to clicked tab
        $(this).addClass('nav-tab-active');
        
        // Show corresponding content
        var target = $(this).attr('href');
        $(target).addClass('active');
    });
    
    // Range slider value update
    $('input[type="range"]').on('input', function() {
        $(this).next('.range-value').text($(this).val());
    });
    
    // Formula input real-time preview
    $('#formula-input').on('input', function() {
        var formula = $(this).val();
        if (formula.trim()) {
            $('#formula-preview-content').html('<strong>Formula:</strong> ' + formula);
        } else {
            $('#formula-preview-content').text('Enter a formula to see the preview...');
        }
    });
});

// Function to insert functions into formula
function insertFunction(func) {
    var textarea = document.getElementById('formula-input');
    var start = textarea.selectionStart;
    var end = textarea.selectionEnd;
    var text = textarea.value;
    
    textarea.value = text.substring(0, start) + func + text.substring(end);
    textarea.focus();
    textarea.setSelectionRange(start + func.length, start + func.length);
    
    // Trigger input event for preview
    jQuery('#formula-input').trigger('input');
}

// Function to insert variables into formula
function insertVariable(variable) {
    var textarea = document.getElementById('formula-input');
    var start = textarea.selectionStart;
    var end = textarea.selectionEnd;
    var text = textarea.value;

    textarea.value = text.substring(0, start) + variable + text.substring(end);
    textarea.focus();
    textarea.setSelectionRange(start + variable.length, start + variable.length);

    // Trigger input event for preview
    jQuery('#formula-input').trigger('input');
}

// Variables Manager Functions
function addVariable() {
    var name = document.getElementById('new-var-name').value.trim();
    var label = document.getElementById('new-var-label').value.trim();
    var value = document.getElementById('new-var-value').value;

    if (!name || !label) {
        alert('Please enter both variable name and label.');
        return;
    }

    // AJAX call to add variable
    jQuery.post(ajaxurl, {
        action: 'cfb_add_variable',
        name: name,
        label: label,
        value: value,
        nonce: '<?php echo wp_create_nonce('cfb_variable_nonce'); ?>'
    }, function(response) {
        if (response.success) {
            location.reload(); // Refresh to show new variable
        } else {
            alert('Error adding variable: ' + response.data);
        }
    });
}

function toggleVariable(id) {
    jQuery.post(ajaxurl, {
        action: 'cfb_toggle_variable',
        id: id,
        nonce: '<?php echo wp_create_nonce('cfb_variable_nonce'); ?>'
    }, function(response) {
        if (response.success) {
            location.reload();
        } else {
            alert('Error toggling variable: ' + response.data);
        }
    });
}

function deleteVariable(id) {
    if (confirm('Are you sure you want to delete this variable?')) {
        jQuery.post(ajaxurl, {
            action: 'cfb_delete_variable',
            id: id,
            nonce: '<?php echo wp_create_nonce('cfb_variable_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert('Error deleting variable: ' + response.data);
            }
        });
    }
}

// Debugging Functions
function debugFormula() {
    var formula = document.getElementById('debug-formula').value.trim();
    if (!formula) {
        alert('Please enter a formula to debug.');
        return;
    }

    jQuery.post(ajaxurl, {
        action: 'cfb_debug_formula',
        formula: formula,
        nonce: '<?php echo wp_create_nonce('cfb_debug_nonce'); ?>'
    }, function(response) {
        var output = document.getElementById('debug-output');
        if (response.success) {
            output.innerHTML = '<div class="debug-success">' + response.data + '</div>';
        } else {
            output.innerHTML = '<div class="debug-error">Error: ' + response.data + '</div>';
        }
    });
}

function testBasicMath() {
    var tests = [
        '2 + 3',
        '10 * 5',
        '100 / 4',
        '(5 + 3) * 2'
    ];

    runQuickTests('Basic Math Tests', tests);
}

function testFunctions() {
    var tests = [
        'ceil(1.1)',
        'floor(1.9)',
        'max(5, 10)',
        'min(5, 10)',
        'round(3.7)',
        'abs(-5)'
    ];

    runQuickTests('Function Tests', tests);
}

function testVariables() {
    var tests = [
        '{dropdown_4}',
        '{paper}',
        '{print}'
    ];

    runQuickTests('Variable Tests', tests);
}

function runQuickTests(title, tests) {
    var output = document.getElementById('quick-test-output');
    output.innerHTML = '<h4>' + title + '</h4>';

    tests.forEach(function(test) {
        jQuery.post(ajaxurl, {
            action: 'cfb_test_formula',
            formula: test,
            nonce: '<?php echo wp_create_nonce('cfb_debug_nonce'); ?>'
        }, function(response) {
            var result = response.success ?
                '<span class="test-success">✅ ' + test + ' = ' + response.data + '</span>' :
                '<span class="test-error">❌ ' + test + ' = Error: ' + response.data + '</span>';
            output.innerHTML += '<div class="test-result">' + result + '</div>';
        });
    });
}

function clearDebugOutput() {
    document.getElementById('debug-output').innerHTML = '';
    document.getElementById('quick-test-output').innerHTML = '';
}

// Show warning when clicking global variables
function showGlobalVarWarning(varName) {
    alert('⚠️ Global Variable Warning\n\n' +
          'You clicked on global variable {' + varName + '}.\n\n' +
          'To use this in formulas, you need to:\n' +
          '1. Create a form field with the same name\n' +
          '2. Set the field type to "hidden" for constants\n' +
          '3. Set the default value to the global variable value\n\n' +
          'Then you can use {' + varName + '} in your formulas.');
}
</script>
