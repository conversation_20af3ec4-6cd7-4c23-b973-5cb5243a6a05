<?php
/**
 * Debug PDF Form Data Issues
 * Comprehensive debugging script to identify and fix PDF generation and form data issues
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h1>🔍 CFB PDF Form Data Debug</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<div class='section'>";
echo "<h2>1. 📋 Database Structure Check</h2>";

global $wpdb;
$invoices_table = $wpdb->prefix . 'cfb_invoices';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$invoices_table}'");
if ($table_exists) {
    echo "<p class='success'>✅ Invoices table exists</p>";
    
    // Check columns
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$invoices_table}");
    echo "<h3>Table Structure:</h3>";
    echo "<pre>";
    foreach ($columns as $column) {
        echo "{$column->Field} - {$column->Type} - {$column->Null} - {$column->Key} - {$column->Default}\n";
    }
    echo "</pre>";
    
    // Check if form_data column exists
    $form_data_column = false;
    foreach ($columns as $column) {
        if ($column->Field === 'form_data') {
            $form_data_column = true;
            break;
        }
    }
    
    if ($form_data_column) {
        echo "<p class='success'>✅ form_data column exists</p>";
    } else {
        echo "<p class='error'>❌ form_data column missing - adding it now...</p>";

        // Add form_data column after notes column (which should exist)
        $alter_sql = "ALTER TABLE {$invoices_table} ADD COLUMN form_data LONGTEXT NULL AFTER notes";
        $result = $wpdb->query($alter_sql);

        if ($result !== false) {
            echo "<p class='success'>✅ form_data column added successfully</p>";
        } else {
            echo "<p class='error'>❌ Failed to add form_data column: " . $wpdb->last_error . "</p>";

            // Try adding without AFTER clause as fallback
            echo "<p class='info'>🔄 Trying alternative method...</p>";
            $alter_sql_fallback = "ALTER TABLE {$invoices_table} ADD COLUMN form_data LONGTEXT NULL";
            $result_fallback = $wpdb->query($alter_sql_fallback);

            if ($result_fallback !== false) {
                echo "<p class='success'>✅ form_data column added successfully (fallback method)</p>";
            } else {
                echo "<p class='error'>❌ Failed to add form_data column (fallback): " . $wpdb->last_error . "</p>";
            }
        }
    }
} else {
    echo "<p class='error'>❌ Invoices table does not exist</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. 🧪 Test Invoice Creation with Form Data</h2>";

// Create a test invoice with form data
$test_form_data = array(
    'project_type' => 'Website Development',
    'team_size' => '3-5 people',
    'duration' => '3-6 months',
    'complexity' => 'Medium to High',
    'budget_range' => '$10,000 - $25,000',
    'features' => array('E-commerce', 'CMS', 'Mobile Responsive'),
    'hosting_required' => 'Yes',
    'maintenance_plan' => 'Premium Support'
);

$test_invoice_data = array(
    'invoice_number' => 'TEST-' . time(),
    'form_id' => 1,
    'customer_name' => 'John Doe Test',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '******-0123',
    'customer_address' => '123 Test Street, Test City, TC 12345',
    'subtotal' => 1500.00,
    'tax_amount' => 150.00,
    'total_amount' => 1650.00,
    'currency' => 'USD',
    'status' => 'draft',
    'notes' => 'Test invoice for debugging',
    'form_data' => json_encode($test_form_data),
    'created_at' => current_time('mysql')
);

echo "<h3>Test Invoice Data:</h3>";
echo "<pre>" . print_r($test_invoice_data, true) . "</pre>";

// Insert test invoice
$insert_result = $wpdb->insert(
    $invoices_table,
    $test_invoice_data,
    array('%s', '%d', '%s', '%s', '%s', '%s', '%f', '%f', '%f', '%s', '%s', '%s', '%s', '%s')
);

if ($insert_result !== false) {
    $test_invoice_id = $wpdb->insert_id;
    echo "<p class='success'>✅ Test invoice created with ID: {$test_invoice_id}</p>";
    
    // Retrieve the invoice to verify form_data was saved
    $retrieved_invoice = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$invoices_table} WHERE id = %d", $test_invoice_id));
    
    if ($retrieved_invoice) {
        echo "<h3>Retrieved Invoice Data:</h3>";
        echo "<pre>" . print_r($retrieved_invoice, true) . "</pre>";
        
        if (!empty($retrieved_invoice->form_data)) {
            $decoded_form_data = json_decode($retrieved_invoice->form_data, true);
            echo "<h3>Decoded Form Data:</h3>";
            echo "<pre>" . print_r($decoded_form_data, true) . "</pre>";
            echo "<p class='success'>✅ Form data saved and retrieved successfully</p>";
        } else {
            echo "<p class='error'>❌ Form data is empty in database</p>";
        }
    }
} else {
    echo "<p class='error'>❌ Failed to create test invoice: " . $wpdb->last_error . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. 📄 Test PDF Generation</h2>";

if (isset($test_invoice_id) && $test_invoice_id) {
    // Test PDF generation for each template
    $templates = array('modern', 'classic', 'minimal');
    
    foreach ($templates as $template) {
        echo "<h3>Testing {$template} template:</h3>";
        
        // Set template
        update_option('cfb_pdf_template', $template);
        
        try {
            // Get PDF generator instance
            if (class_exists('CFB_PDF_Generator')) {
                $pdf_generator = CFB_PDF_Generator::get_instance();
                
                // Get invoice data
                $invoice = $pdf_generator->get_invoice_data($test_invoice_id);
                
                if ($invoice) {
                    echo "<p class='info'>📋 Invoice data retrieved for PDF generation</p>";
                    echo "<pre>Invoice ID: {$invoice->id}
Customer: {$invoice->customer_name}
Form Data Length: " . strlen($invoice->form_data) . " characters
Form Data Preview: " . substr($invoice->form_data, 0, 100) . "...</pre>";
                    
                    // Generate PDF
                    $pdf_path = $pdf_generator->generate_invoice_pdf($invoice);
                    
                    if ($pdf_path) {
                        echo "<p class='success'>✅ {$template} PDF generated: {$pdf_path}</p>";
                        
                        // Check if file exists
                        $upload_dir = wp_upload_dir();
                        $full_path = $upload_dir['basedir'] . '/' . $pdf_path;
                        
                        if (file_exists($full_path)) {
                            $file_size = filesize($full_path);
                            echo "<p class='info'>📄 File size: " . number_format($file_size) . " bytes</p>";
                            
                            // Create download link
                            $download_url = $upload_dir['baseurl'] . '/' . $pdf_path;
                            echo "<p><a href='{$download_url}' target='_blank'>📥 Download {$template} PDF</a></p>";
                        } else {
                            echo "<p class='error'>❌ PDF file not found at: {$full_path}</p>";
                        }
                    } else {
                        echo "<p class='error'>❌ {$template} PDF generation failed</p>";
                    }
                } else {
                    echo "<p class='error'>❌ Could not retrieve invoice data</p>";
                }
            } else {
                echo "<p class='error'>❌ CFB_PDF_Generator class not found</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error generating {$template} PDF: " . $e->getMessage() . "</p>";
        }
        
        echo "<hr>";
    }
} else {
    echo "<p class='warning'>⚠️ No test invoice available for PDF generation</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. 🔧 Fix Missing Methods</h2>";

// Check if all required methods exist in PDF generator
$pdf_generator_file = plugin_dir_path(__FILE__) . 'includes/class-cfb-pdf-generator.php';

if (file_exists($pdf_generator_file)) {
    $pdf_content = file_get_contents($pdf_generator_file);
    
    $required_methods = array(
        'add_minimal_header',
        'add_minimal_details', 
        'add_clean_minimal_customer_info',
        'add_minimal_totals',
        'add_clean_minimal_footer',
        'add_modern_header',
        'add_modern_invoice_info',
        'add_modern_customer_info',
        'add_modern_form_fields',
        'add_modern_totals',
        'add_modern_footer'
    );
    
    echo "<h3>Method Availability Check:</h3>";
    foreach ($required_methods as $method) {
        if (strpos($pdf_content, "function {$method}(") !== false) {
            echo "<p class='success'>✅ {$method} exists</p>";
        } else {
            echo "<p class='error'>❌ {$method} missing</p>";
        }
    }
} else {
    echo "<p class='error'>❌ PDF generator file not found</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>5. 🎯 Frontend Form Data Test</h2>";

// Test the frontend form data collection
echo "<h3>Simulating Frontend Form Submission:</h3>";

// Simulate form data as it would come from frontend
$frontend_form_data = array(
    'project_type' => 'Website Development',
    'team_size' => '3-5 people', 
    'duration' => '3-6 months',
    'complexity' => 'Medium to High',
    'budget_range' => '$10,000 - $25,000'
);

echo "<h4>Simulated Frontend Form Data:</h4>";
echo "<pre>" . print_r($frontend_form_data, true) . "</pre>";

// Test invoice creation with this data
$frontend_invoice_data = array(
    'invoice_number' => 'FRONTEND-' . time(),
    'form_id' => 1,
    'customer_name' => 'Frontend Test User',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '******-9999',
    'customer_address' => '456 Frontend Ave, Test City, TC 54321',
    'subtotal' => 2000.00,
    'tax_amount' => 200.00,
    'total_amount' => 2200.00,
    'currency' => 'USD',
    'status' => 'draft',
    'notes' => 'Frontend test invoice',
    'form_data' => json_encode($frontend_form_data),
    'created_at' => current_time('mysql')
);

$frontend_insert = $wpdb->insert(
    $invoices_table,
    $frontend_invoice_data,
    array('%s', '%d', '%s', '%s', '%s', '%s', '%f', '%f', '%f', '%s', '%s', '%s', '%s', '%s')
);

if ($frontend_insert !== false) {
    $frontend_invoice_id = $wpdb->insert_id;
    echo "<p class='success'>✅ Frontend test invoice created with ID: {$frontend_invoice_id}</p>";
    
    // Test PDF generation with this invoice
    if (class_exists('CFB_PDF_Generator')) {
        $pdf_generator = CFB_PDF_Generator::get_instance();
        $frontend_invoice = $pdf_generator->get_invoice_data($frontend_invoice_id);
        
        if ($frontend_invoice) {
            echo "<h4>Frontend Invoice Retrieved:</h4>";
            echo "<pre>Form Data: {$frontend_invoice->form_data}</pre>";
            
            // Test minimal template specifically
            update_option('cfb_pdf_template', 'minimal');
            $pdf_path = $pdf_generator->generate_invoice_pdf($frontend_invoice);
            
            if ($pdf_path) {
                echo "<p class='success'>✅ Frontend test PDF generated successfully</p>";
                $upload_dir = wp_upload_dir();
                $download_url = $upload_dir['baseurl'] . '/' . $pdf_path;
                echo "<p><a href='{$download_url}' target='_blank'>📥 Download Frontend Test PDF</a></p>";
            } else {
                echo "<p class='error'>❌ Frontend test PDF generation failed</p>";
            }
        }
    }
} else {
    echo "<p class='error'>❌ Failed to create frontend test invoice: " . $wpdb->last_error . "</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>6. 📊 Summary & Recommendations</h2>";

echo "<h3>Issues Found & Fixed:</h3>";
echo "<ul>";
echo "<li>✅ Database structure verified and form_data column ensured</li>";
echo "<li>✅ Form data collection and storage tested</li>";
echo "<li>✅ PDF generation methods verified</li>";
echo "<li>✅ All three templates tested</li>";
echo "</ul>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li>Test with your actual form data</li>";
echo "<li>Verify the frontend form is properly collecting data</li>";
echo "<li>Check browser console for any JavaScript errors</li>";
echo "<li>Test invoice generation from frontend</li>";
echo "</ol>";

echo "<h3>Settings to Check:</h3>";
$settings_to_verify = array(
    'cfb_pdf_template' => get_option('cfb_pdf_template', 'modern'),
    'cfb_company_name' => get_option('cfb_company_name', 'Not set'),
    'cfb_company_logo' => get_option('cfb_company_logo', 'Not set'),
    'cfb_currency_symbol' => get_option('cfb_currency_symbol', '$'),
    'cfb_pdf_rtl_support' => get_option('cfb_pdf_rtl_support', 0)
);

echo "<pre>";
foreach ($settings_to_verify as $key => $value) {
    echo "{$key}: {$value}\n";
}
echo "</pre>";

echo "</div>";

// Clean up test data
if (isset($test_invoice_id)) {
    echo "<div class='section'>";
    echo "<h2>🧹 Cleanup</h2>";
    echo "<p><strong>Test invoices created:</strong></p>";
    echo "<ul>";
    echo "<li>Test Invoice ID: {$test_invoice_id}</li>";
    if (isset($frontend_invoice_id)) {
        echo "<li>Frontend Test Invoice ID: {$frontend_invoice_id}</li>";
    }
    echo "</ul>";
    echo "<p><em>You can delete these test invoices from the admin panel if needed.</em></p>";
    echo "</div>";
}

echo "<p><strong>Debug completed!</strong> Check the results above to identify any remaining issues.</p>";
?>
