<?php
/**
 * Fix CFB Database Structure
 * This script ensures the database has the correct structure for PDF generation
 */

// Load WordPress
require_once('../../../wp-config.php');

echo "<h1>🔧 CFB Database Structure Fix</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

global $wpdb;

echo "<div class='section'>";
echo "<h2>1. 📋 Current Database Structure</h2>";

$invoices_table = $wpdb->prefix . 'cfb_invoices';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$invoices_table}'");

if ($table_exists) {
    echo "<p class='success'>✅ Invoices table exists</p>";
    
    // Get current structure
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$invoices_table}");
    echo "<h3>Current Table Structure:</h3>";
    echo "<pre>";
    foreach ($columns as $column) {
        echo sprintf("%-20s %-20s %-10s %-10s %s\n", 
            $column->Field, 
            $column->Type, 
            $column->Null, 
            $column->Key, 
            $column->Default
        );
    }
    echo "</pre>";
    
} else {
    echo "<p class='error'>❌ Invoices table does not exist</p>";
    echo "<p class='info'>Creating invoices table...</p>";
    
    // Create the table with correct structure
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE {$invoices_table} (
        id int(11) NOT NULL AUTO_INCREMENT,
        invoice_number varchar(50) NOT NULL,
        form_id int(11) NOT NULL,
        submission_id int(11),
        customer_name varchar(255) NOT NULL,
        customer_email varchar(255) NOT NULL,
        customer_phone varchar(50),
        customer_address text,
        subtotal decimal(10,2) NOT NULL DEFAULT 0,
        tax_amount decimal(10,2) NOT NULL DEFAULT 0,
        total_amount decimal(10,2) NOT NULL DEFAULT 0,
        currency varchar(10) DEFAULT 'USD',
        status varchar(20) DEFAULT 'draft',
        pdf_path varchar(500),
        notes text,
        form_data longtext,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY invoice_number (invoice_number),
        KEY form_id (form_id),
        KEY submission_id (submission_id),
        KEY status (status),
        KEY created_at (created_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Check if creation was successful
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$invoices_table}'");
    if ($table_exists) {
        echo "<p class='success'>✅ Invoices table created successfully</p>";
    } else {
        echo "<p class='error'>❌ Failed to create invoices table</p>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>2. 🔍 Check Required Columns</h2>";

if ($table_exists) {
    $required_columns = array(
        'id' => 'int(11)',
        'invoice_number' => 'varchar(50)',
        'form_id' => 'int(11)',
        'customer_name' => 'varchar(255)',
        'customer_email' => 'varchar(255)',
        'subtotal' => 'decimal(10,2)',
        'tax_amount' => 'decimal(10,2)',
        'total_amount' => 'decimal(10,2)',
        'form_data' => 'longtext'
    );
    
    $current_columns = array();
    $columns = $wpdb->get_results("SHOW COLUMNS FROM {$invoices_table}");
    foreach ($columns as $column) {
        $current_columns[$column->Field] = $column->Type;
    }
    
    $missing_columns = array();
    $incorrect_types = array();
    
    foreach ($required_columns as $col_name => $col_type) {
        if (!isset($current_columns[$col_name])) {
            $missing_columns[] = $col_name;
        } elseif (strpos(strtolower($current_columns[$col_name]), strtolower($col_type)) === false) {
            $incorrect_types[$col_name] = array(
                'current' => $current_columns[$col_name],
                'required' => $col_type
            );
        }
    }
    
    if (empty($missing_columns) && empty($incorrect_types)) {
        echo "<p class='success'>✅ All required columns exist with correct types</p>";
    } else {
        if (!empty($missing_columns)) {
            echo "<p class='error'>❌ Missing columns: " . implode(', ', $missing_columns) . "</p>";
        }
        if (!empty($incorrect_types)) {
            echo "<p class='warning'>⚠️ Incorrect column types:</p>";
            foreach ($incorrect_types as $col => $types) {
                echo "<p class='info'>  - {$col}: current={$types['current']}, required={$types['required']}</p>";
            }
        }
    }
    
    // Fix missing form_data column specifically
    if (in_array('form_data', $missing_columns)) {
        echo "<p class='info'>🔧 Adding form_data column...</p>";
        
        $alter_sql = "ALTER TABLE {$invoices_table} ADD COLUMN form_data LONGTEXT NULL";
        $result = $wpdb->query($alter_sql);
        
        if ($result !== false) {
            echo "<p class='success'>✅ form_data column added successfully</p>";
        } else {
            echo "<p class='error'>❌ Failed to add form_data column: " . $wpdb->last_error . "</p>";
        }
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>3. 🧪 Test Database Operations</h2>";

if ($table_exists) {
    // Test insert
    $test_data = array(
        'invoice_number' => 'DB-TEST-' . time(),
        'form_id' => 1,
        'customer_name' => 'Database Test User',
        'customer_email' => '<EMAIL>',
        'subtotal' => 100.00,
        'tax_amount' => 10.00,
        'total_amount' => 110.00,
        'form_data' => json_encode(array(
            'test_field' => 'test_value',
            'project_type' => 'Database Test'
        ))
    );
    
    $insert_result = $wpdb->insert(
        $invoices_table,
        $test_data,
        array('%s', '%d', '%s', '%s', '%f', '%f', '%f', '%s')
    );
    
    if ($insert_result !== false) {
        $test_id = $wpdb->insert_id;
        echo "<p class='success'>✅ Test insert successful - ID: {$test_id}</p>";
        
        // Test select
        $retrieved = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$invoices_table} WHERE id = %d", $test_id));
        
        if ($retrieved && !empty($retrieved->form_data)) {
            echo "<p class='success'>✅ Test select successful - form_data retrieved</p>";
            
            $decoded_data = json_decode($retrieved->form_data, true);
            if (is_array($decoded_data) && isset($decoded_data['test_field'])) {
                echo "<p class='success'>✅ JSON data properly stored and retrieved</p>";
            } else {
                echo "<p class='error'>❌ JSON data not properly decoded</p>";
            }
            
            // Clean up test data
            $wpdb->delete($invoices_table, array('id' => $test_id), array('%d'));
            echo "<p class='info'>🧹 Test data cleaned up</p>";
            
        } else {
            echo "<p class='error'>❌ Test select failed or form_data is empty</p>";
        }
    } else {
        echo "<p class='error'>❌ Test insert failed: " . $wpdb->last_error . "</p>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>4. 📊 Database Summary</h2>";

if ($table_exists) {
    // Count existing invoices
    $invoice_count = $wpdb->get_var("SELECT COUNT(*) FROM {$invoices_table}");
    echo "<p class='info'>📄 Total invoices in database: {$invoice_count}</p>";
    
    // Count invoices with form_data
    $form_data_count = $wpdb->get_var("SELECT COUNT(*) FROM {$invoices_table} WHERE form_data IS NOT NULL AND form_data != ''");
    echo "<p class='info'>📋 Invoices with form data: {$form_data_count}</p>";
    
    // Show recent invoices
    $recent_invoices = $wpdb->get_results("SELECT id, invoice_number, customer_name, created_at, CHAR_LENGTH(form_data) as form_data_length FROM {$invoices_table} ORDER BY created_at DESC LIMIT 5");
    
    if (!empty($recent_invoices)) {
        echo "<h3>Recent Invoices:</h3>";
        echo "<pre>";
        echo sprintf("%-5s %-15s %-20s %-20s %-15s\n", "ID", "Invoice #", "Customer", "Created", "Form Data");
        echo str_repeat("-", 80) . "\n";
        foreach ($recent_invoices as $invoice) {
            echo sprintf("%-5s %-15s %-20s %-20s %-15s\n", 
                $invoice->id,
                $invoice->invoice_number,
                substr($invoice->customer_name, 0, 18),
                substr($invoice->created_at, 0, 16),
                $invoice->form_data_length ? $invoice->form_data_length . ' chars' : 'Empty'
            );
        }
        echo "</pre>";
    }
}

echo "<h3>✅ Database Fix Complete</h3>";
echo "<p>The database structure has been verified and fixed. You can now:</p>";
echo "<ul>";
echo "<li>✅ Create invoices with form data</li>";
echo "<li>✅ Generate PDFs with real form data</li>";
echo "<li>✅ Use all three PDF templates</li>";
echo "</ul>";

echo "</div>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Run the debug script: <code>debug-pdf-form-data.php</code></li>";
echo "<li>Test PDF generation with all three templates</li>";
echo "<li>Verify form data appears correctly in PDFs</li>";
echo "</ol>";
?>
