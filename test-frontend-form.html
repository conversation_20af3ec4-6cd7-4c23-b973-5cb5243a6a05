<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CFB Frontend Form Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-field {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus, select:focus, textarea:focus {
            border-color: #4CAF50;
            outline: none;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            background-color: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }
        .success {
            background-color: #e8f5e8;
            border-left-color: #4CAF50;
            color: #2e7d32;
        }
        .invoice-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f0f8ff;
            border-radius: 5px;
            border: 2px solid #2196F3;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-col {
            flex: 1;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="test-container">
        <h1>🧪 CFB Frontend Form Test</h1>
        <p>This test simulates the frontend form data collection and invoice generation process.</p>

        <form id="test-form">
            <div class="form-field">
                <label for="project_type">Project Type:</label>
                <select id="project_type" name="project_type" required>
                    <option value="">Select project type...</option>
                    <option value="website">Website Development</option>
                    <option value="app">Mobile App</option>
                    <option value="ecommerce">E-commerce Store</option>
                    <option value="custom">Custom Software</option>
                </select>
            </div>

            <div class="form-field">
                <label for="team_size">Team Size:</label>
                <select id="team_size" name="team_size" required>
                    <option value="">Select team size...</option>
                    <option value="1-2">1-2 people</option>
                    <option value="3-5">3-5 people</option>
                    <option value="6-10">6-10 people</option>
                    <option value="10+">10+ people</option>
                </select>
            </div>

            <div class="form-field">
                <label for="duration">Project Duration:</label>
                <select id="duration" name="duration" required>
                    <option value="">Select duration...</option>
                    <option value="1-3 months">1-3 months</option>
                    <option value="3-6 months">3-6 months</option>
                    <option value="6-12 months">6-12 months</option>
                    <option value="12+ months">12+ months</option>
                </select>
            </div>

            <div class="form-field">
                <label for="complexity">Project Complexity:</label>
                <select id="complexity" name="complexity" required>
                    <option value="">Select complexity...</option>
                    <option value="Simple">Simple</option>
                    <option value="Medium">Medium</option>
                    <option value="High">High</option>
                    <option value="Very High">Very High</option>
                </select>
            </div>

            <div class="form-field">
                <label for="budget_range">Budget Range:</label>
                <select id="budget_range" name="budget_range" required>
                    <option value="">Select budget range...</option>
                    <option value="$1,000 - $5,000">$1,000 - $5,000</option>
                    <option value="$5,000 - $10,000">$5,000 - $10,000</option>
                    <option value="$10,000 - $25,000">$10,000 - $25,000</option>
                    <option value="$25,000+">$25,000+</option>
                </select>
            </div>

            <div class="form-field">
                <label for="additional_notes">Additional Notes:</label>
                <textarea id="additional_notes" name="additional_notes" rows="3" placeholder="Any additional requirements or notes..."></textarea>
            </div>

            <button type="button" class="btn" onclick="calculatePrice()">Calculate Price</button>
            <button type="button" class="btn" onclick="collectFormData()">Test Form Data Collection</button>
        </form>

        <div id="results" style="display: none;"></div>

        <div id="invoice-section" class="invoice-section" style="display: none;">
            <h3>📄 Invoice Information</h3>
            <div class="form-row">
                <div class="form-col">
                    <label for="customer_name">Full Name *</label>
                    <input type="text" id="customer_name" name="customer_name" required>
                </div>
                <div class="form-col">
                    <label for="customer_email">Email Address *</label>
                    <input type="email" id="customer_email" name="customer_email" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label for="customer_phone">Phone Number</label>
                    <input type="tel" id="customer_phone" name="customer_phone">
                </div>
                <div class="form-col">
                    <label for="customer_address">Address</label>
                    <textarea id="customer_address" name="customer_address" rows="2"></textarea>
                </div>
            </div>
            <button type="button" class="btn" onclick="generateInvoice()">Generate Test Invoice</button>
        </div>
    </div>

    <script>
        let calculationData = null;

        function collectFormData() {
            const formData = {};
            
            // Collect all form fields
            $('#test-form').find('[name]').each(function() {
                const field = $(this);
                const name = field.attr('name');
                const value = field.val();
                
                if (value) {
                    formData[name] = value;
                }
            });

            showResults('Form Data Collected', formData, 'success');
            return formData;
        }

        function calculatePrice() {
            const formData = collectFormData();
            
            if (Object.keys(formData).length === 0) {
                showResults('Please fill in at least one field', null, 'error');
                return;
            }

            // Simulate calculation
            const basePrice = 5000;
            const complexityMultiplier = {
                'Simple': 1,
                'Medium': 1.5,
                'High': 2,
                'Very High': 3
            };
            
            const teamMultiplier = {
                '1-2': 1,
                '3-5': 1.3,
                '6-10': 1.6,
                '10+': 2
            };

            const complexity = formData.complexity || 'Medium';
            const teamSize = formData.team_size || '3-5';
            
            const total = basePrice * (complexityMultiplier[complexity] || 1.5) * (teamMultiplier[teamSize] || 1.3);
            
            calculationData = {
                total: total,
                breakdown: {
                    base: basePrice,
                    complexity_factor: complexityMultiplier[complexity] || 1.5,
                    team_factor: teamMultiplier[teamSize] || 1.3
                },
                form_data: formData
            };

            showResults('Calculation Complete', {
                total: '$' + total.toLocaleString(),
                form_data: formData
            }, 'success');

            // Show invoice section
            $('#invoice-section').slideDown();
        }

        function generateInvoice() {
            if (!calculationData) {
                showResults('Please calculate price first', null, 'error');
                return;
            }

            const customerName = $('#customer_name').val().trim();
            const customerEmail = $('#customer_email').val().trim();

            if (!customerName || !customerEmail) {
                showResults('Please fill in required customer information', null, 'error');
                return;
            }

            const invoiceData = {
                customer_name: customerName,
                customer_email: customerEmail,
                customer_phone: $('#customer_phone').val().trim(),
                customer_address: $('#customer_address').val().trim(),
                form_id: 1, // Test form ID
                subtotal: calculationData.total,
                tax_amount: 0,
                total_amount: calculationData.total,
                form_data: calculationData.form_data,
                calculation_data: calculationData
            };

            showResults('Invoice Data Prepared', invoiceData, 'success');

            // Test AJAX call to create invoice
            testInvoiceCreation(invoiceData);
        }

        function testInvoiceCreation(invoiceData) {
            const ajaxData = {
                action: 'cfb_save_invoice',
                nonce: 'test_nonce', // This would be real nonce in actual implementation
                ...invoiceData
            };

            showResults('AJAX Data for Invoice Creation', ajaxData, 'success');

            // In real implementation, this would make actual AJAX call:
            /*
            $.ajax({
                url: '/wp-admin/admin-ajax.php',
                type: 'POST',
                data: ajaxData,
                success: function(response) {
                    if (response.success) {
                        testPDFGeneration(response.data.invoice_id);
                    } else {
                        showResults('Invoice Creation Failed', response.data, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    showResults('Network Error', error, 'error');
                }
            });
            */
        }

        function showResults(title, data, type = 'success') {
            const resultsDiv = $('#results');
            const className = type === 'error' ? 'results error' : 'results success';
            
            let content = `<h3>${title}</h3>`;
            
            if (data) {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            resultsDiv.attr('class', className).html(content).show();
        }

        // Initialize
        $(document).ready(function() {
            console.log('CFB Frontend Form Test initialized');
        });
    </script>
</body>
</html>
